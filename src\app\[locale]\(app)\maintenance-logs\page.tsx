'use client';

import { StatusCard } from '@/components/status-card';
import { MaintenanceLogsFilterSection } from '@/features/maintenance-logs/components/MaintenanceLogsFilterSection';
import MaintenanceLogsTable from '@/features/maintenance-logs/components/MaintenanceLogsTable';
import { MaintenancePageSkeleton } from '@/features/maintenance-logs/components/MaintenancePageSkeleton';
import {
  useMaintenanceLogs,
  useStatusCounts,
} from '@/features/maintenance-logs/hooks';
import type { MaintenanceTableState } from '@/features/maintenance-logs/types/table';
import {
  ALL_COLUMNS,
  DEFAULT_TABLE_STATE,
} from '@/features/maintenance-logs/types/table';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function MaintenanceLogsPage() {
  const router = useRouter();
  const t = useTranslations('pages.maintenanceLogs.page');
  const [tableState, setTableState] =
    useState<MaintenanceTableState>(DEFAULT_TABLE_STATE);

  // Use the new TanStack Query hook
  const {
    data: logsResponse,
    isLoading,
    error,
  } = useMaintenanceLogs({
    filters: tableState.filters,
    pageIndex: tableState.pageIndex,
    pageSize: tableState.pageSize,
    sorting: tableState.sorting,
  });

  // Use the status counts hook for accurate status card data
  const {
    data: statusCounts,
    isLoading: isLoadingStatusCounts,
    error: statusCountsError,
  } = useStatusCounts();

  // Handle loading and error states
  if (error) {
    console.error('Error loading maintenance logs:', error);
  }
  if (statusCountsError) {
    console.error('Error loading status counts:', statusCountsError);
  }

  const logs = logsResponse?.data || [];
  const totalItems = logsResponse?.totalCount || 0;

  // Enhanced loading state - show skeleton if either data is loading
  if (isLoading || isLoadingStatusCounts) {
    return <MaintenancePageSkeleton />;
  }

  // Enhanced error state - show error if either query fails and neither is loading
  if ((error || statusCountsError) && !isLoading && !isLoadingStatusCounts) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-12 h-12 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {t('errorTitle')}
              </h1>
              <p className="text-lg text-gray-600 mb-8">
                {t('errorDescription')}
              </p>
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-6 py-3 bg-gray-900 text-white font-medium rounded-lg hover:bg-gray-800 transition-colors"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                {t('tryAgain')}
              </button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Calculate total pages for pagination
  const totalPages = Math.max(1, Math.ceil(totalItems / tableState.pageSize));

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setTableState((prev) => ({ ...prev, pageIndex: page - 1 }));
  };
  const handlePageSizeChange = (size: number) => {
    setTableState((prev) => ({ ...prev, pageSize: size, pageIndex: 0 }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50/50 via-white to-blue-50/30">
      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Hero Section */}
        <div className="mb-8 sm:mb-12">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 tracking-tight">
              {t('dashboardTitle')}
            </h1>
            <p className="text-gray-600 mt-2 sm:mt-3 text-sm sm:text-base lg:text-lg max-w-2xl">
              {t('dashboardDescription')}
            </p>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12">
          <StatusCard
            title={t('cards.fullyFunctional.title')}
            value={statusCounts?.inService || 0}
            description={t('cards.fullyFunctional.description')}
            icon={
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            }
            variant="success"
          />

          <StatusCard
            title={t('cards.broken.title')}
            value={statusCounts?.outOfService || 0}
            description={t('cards.broken.description')}
            icon={
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            }
            variant="default"
          />
        </div>

        {/* Data Section */}
        <section className="space-y-6 sm:space-y-8">
          {/* Section Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">
                {t('recordsTitle')}
              </h2>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                {totalItems > 0
                  ? t('recordsFound', { count: totalItems.toLocaleString() })
                  : t('noRecords')}
              </p>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-3">
              <button
                className="inline-flex items-center px-4 py-2.5 text-sm font-medium text-primary-foreground bg-primary rounded-xl hover:bg-primary/90 transition-all duration-200 shadow-sm hover:shadow-lg"
                onClick={() => router.push('/maintenance-logs/create')}
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                {t('addRecord')}
              </button>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
            {/* Table Header with Integrated Filters */}
            <div className="px-6 sm:px-8 py-4 sm:py-6 border-b border-gray-200/60 bg-gradient-to-r from-gray-50/50 to-gray-100/30">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2.5 bg-white rounded-xl shadow-sm ring-1 ring-gray-200/50">
                    <svg
                      className="w-5 h-5 text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                      {t('tableTitle')}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-500 mt-0.5">
                      {totalItems > 0
                        ? t('tableRecords', {
                            count: totalItems.toLocaleString(),
                          })
                        : t('noData')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {totalItems > 0 && (
                    <div className="flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg shadow-sm ring-1 ring-gray-200/50">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs sm:text-sm text-gray-600 font-medium">
                        {t('pageOf', {
                          current: tableState.pageIndex + 1,
                          total: Math.ceil(totalItems / tableState.pageSize),
                        })}
                      </span>
                    </div>
                  )}
                  <div className="w-full lg:w-auto lg:max-w-lg">
                    <MaintenanceLogsFilterSection
                      filters={tableState.filters}
                      onFilterChange={(filters: typeof tableState.filters) =>
                        setTableState((prev) => ({ ...prev, filters }))
                      }
                      tableState={tableState}
                      onTableStateChange={setTableState}
                      columns={ALL_COLUMNS}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Table Content */}
            <div className="overflow-x-auto">
              <MaintenanceLogsTable
                data={logs}
                columns={ALL_COLUMNS}
                tableState={tableState}
                onTableStateChange={setTableState}
                isLoading={isLoading}
                totalItems={totalItems}
                currentPage={tableState.pageIndex + 1}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                pageSize={tableState.pageSize}
                onPageSizeChange={handlePageSizeChange}
              />
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
