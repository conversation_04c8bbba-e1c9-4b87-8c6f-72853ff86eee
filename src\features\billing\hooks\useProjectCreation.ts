import type { PmaSubscriptionWithDetails } from '@/features/billing/services/pma-subscriptions.service';
import { pmaSubscriptionsService } from '@/features/billing/services/pma-subscriptions.service';
import { ContractorPackageService } from '@/features/billing/services/contractor-package.service';
import type {
  Project,
  ProjectFormData,
} from '@/features/projects/types/project';
import {
  getPMACertificateIds,
  createPMACertificateForProject,
} from '@/features/pma-management/services/pma-certificate-service';
import { createCompetentPerson } from '@/features/competent-persons/services/competent-person-service';
import { useUserWithProfile } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { useInvalidateProjectAccess } from './useProjectAccess';

// ================================
// PROJECT CREATION TYPES
// ================================

export interface ProjectCreationStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  current: boolean;
  optional?: boolean;
}

export interface ProjectWithBilling extends Project {
  pmaSubscriptions?: PmaSubscriptionWithDetails[];
}

export interface ProjectCreationResult {
  project: ProjectWithBilling;
  pmaSubscriptions: PmaSubscriptionWithDetails[];
  nextSteps: string[];
}

export interface ProjectCreationFlowState {
  currentStep: number;
  steps: ProjectCreationStep[];
  project?: ProjectWithBilling;
  pmaSubscriptions?: PmaSubscriptionWithDetails[];
  isLoading: boolean;
  error?: string;
}

// ================================
// PROJECT CREATION MUTATIONS
// ================================

/**
 * Create a project with integrated billing setup
 */
export function useCreateProjectWithBilling() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useMutation({
    mutationFn: async (
      projectData: ProjectFormData,
    ): Promise<ProjectCreationResult> => {
      if (!user || !contractorId) {
        throw new Error('User not authenticated or missing contractor profile');
      }

      // Call server-side API to create project with billing
      const response = await fetch('/api/projects/create-with-billing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || 'Failed to create project with billing',
        );
      }

      const result = await response.json();

      return {
        project: result.project as ProjectWithBilling,
        pmaSubscriptions: result.pmaSubscriptions,
        nextSteps: result.nextSteps,
      };
    },
    onSuccess: (data) => {
      const { project, pmaSubscriptions } = data;

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['projects', contractorId, user?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['projects', 'stats', contractorId, user?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['pma-subscriptions', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['pma-access', project.id],
      });

      // Invalidate project access
      invalidateProjectAccess(project.id, contractorId || undefined, user?.id);

      const _pmaCount = pmaSubscriptions.length;
      const description = `Project "${project.name}" has been created successfully. Add PMA certificates to enable billing.`;

      toast({
        title: 'Project Created Successfully',
        description,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Create Project',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

// ================================
// PROJECT CREATION FLOW MANAGEMENT
// ================================

const DEFAULT_CREATION_STEPS: ProjectCreationStep[] = [
  {
    id: 'project-details',
    title: 'Project Details',
    description: 'Enter basic project information',
    completed: false,
    current: true,
  },
  {
    id: 'billing-setup',
    title: 'Billing Setup',
    description: 'Configure subscription and payment',
    completed: false,
    current: false,
  },
  {
    id: 'team-setup',
    title: 'Team Setup',
    description: 'Add team members and assign roles',
    completed: false,
    current: false,
    optional: true,
  },
  {
    id: 'completion',
    title: 'Complete',
    description: 'Review and finalize project creation',
    completed: false,
    current: false,
  },
];

/**
 * Manage the multi-step project creation flow
 */
export function useProjectCreationFlow() {
  const [flowState, setFlowState] = useState<ProjectCreationFlowState>({
    currentStep: 0,
    steps: DEFAULT_CREATION_STEPS,
    isLoading: false,
  });

  const nextStep = useCallback(() => {
    setFlowState((prev) => {
      const newCurrentStep = Math.min(
        prev.currentStep + 1,
        prev.steps.length - 1,
      );
      const updatedSteps = prev.steps.map((step, index) => ({
        ...step,
        completed: index < newCurrentStep,
        current: index === newCurrentStep,
      }));

      return {
        ...prev,
        currentStep: newCurrentStep,
        steps: updatedSteps,
      };
    });
  }, []);

  const previousStep = useCallback(() => {
    setFlowState((prev) => {
      const newCurrentStep = Math.max(prev.currentStep - 1, 0);
      const updatedSteps = prev.steps.map((step, index) => ({
        ...step,
        completed: index < newCurrentStep,
        current: index === newCurrentStep,
      }));

      return {
        ...prev,
        currentStep: newCurrentStep,
        steps: updatedSteps,
      };
    });
  }, []);

  const goToStep = useCallback((stepIndex: number) => {
    setFlowState((prev) => {
      const newCurrentStep = Math.max(
        0,
        Math.min(stepIndex, prev.steps.length - 1),
      );
      const updatedSteps = prev.steps.map((step, index) => ({
        ...step,
        completed: index < newCurrentStep,
        current: index === newCurrentStep,
      }));

      return {
        ...prev,
        currentStep: newCurrentStep,
        steps: updatedSteps,
      };
    });
  }, []);

  const completeCurrentStep = useCallback(() => {
    setFlowState((prev) => {
      const updatedSteps = prev.steps.map((step, index) => ({
        ...step,
        completed: index <= prev.currentStep,
      }));

      return {
        ...prev,
        steps: updatedSteps,
      };
    });
  }, []);

  const setProjectData = useCallback((project: ProjectWithBilling) => {
    setFlowState((prev) => ({
      ...prev,
      project,
    }));
  }, []);

  const setPmaSubscriptionsData = useCallback(
    (pmaSubscriptions: PmaSubscriptionWithDetails[]) => {
      setFlowState((prev) => ({
        ...prev,
        pmaSubscriptions,
      }));
    },
    [],
  );

  const setLoading = useCallback((isLoading: boolean) => {
    setFlowState((prev) => ({
      ...prev,
      isLoading,
    }));
  }, []);

  const setError = useCallback((error?: string) => {
    setFlowState((prev) => ({
      ...prev,
      error,
    }));
  }, []);

  const resetFlow = useCallback(() => {
    setFlowState({
      currentStep: 0,
      steps: DEFAULT_CREATION_STEPS,
      isLoading: false,
    });
  }, []);

  return {
    flowState,
    nextStep,
    previousStep,
    goToStep,
    completeCurrentStep,
    setProjectData,
    setPmaSubscriptionsData,
    setLoading,
    setError,
    resetFlow,
    isFirstStep: flowState.currentStep === 0,
    isLastStep: flowState.currentStep === flowState.steps.length - 1,
    canGoNext: !flowState.isLoading && !flowState.error,
    canGoPrevious: flowState.currentStep > 0 && !flowState.isLoading,
  };
}

// ================================
// PROJECT CREATION NAVIGATION
// ================================

/**
 * Handle navigation after successful project creation
 */
export function useProjectCreationNavigation() {
  const router = useRouter();

  const navigateToProject = useCallback(
    (projectId: string) => {
      router.push(`/projects/${projectId}`);
    },
    [router],
  );

  const navigateToPayment = useCallback(
    (projectId: string) => {
      router.push(`/billing/payment?projectId=${projectId}`);
    },
    [router],
  );

  const navigateToBilling = useCallback(
    (projectId: string) => {
      router.push(`/billing/subscription?projectId=${projectId}`);
    },
    [router],
  );

  const navigateToProjects = useCallback(() => {
    router.push('/projects');
  }, [router]);

  return {
    navigateToProject,
    navigateToPayment,
    navigateToBilling,
    navigateToProjects,
  };
}

// ================================
// PROJECT CREATION PERMISSIONS
// ================================

/**
 * Check if user can create projects
 */
export function useCanCreateProject() {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['can-create-project', user?.id],
    queryFn: async () => {
      if (!user) return false;

      // Only contractors can create projects
      if (user.profile?.user_role !== 'contractor') {
        return false;
      }

      // Check if contractor has valid profile
      if (!user.profile?.contractor_id) {
        return false;
      }

      // TODO: Add additional business rules if needed
      // - Check subscription limits
      // - Check account status
      // - Check payment history

      return true;
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// ================================
// PROJECT CREATION COST CALCULATION
// ================================

/**
 * Calculate the cost of creating a new project
 */
export function useProjectCreationCost() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['project-creation-cost', contractorId],
    queryFn: async () => {
      let monthlyAmount = 150.0; // Default fallback

      if (contractorId) {
        const pricingResult =
          await ContractorPackageService.getContractorPricing(contractorId);
        if (pricingResult.success && pricingResult.data) {
          monthlyAmount = pricingResult.data;
        }
      }

      return {
        monthlyAmount,
        currency: 'MYR',
        setupFee: 0, // No setup fee currently
        totalCost: monthlyAmount,
        description: 'Monthly subscription fee for project access',
      };
    },
    enabled: !!contractorId,
    staleTime: 10 * 60 * 1000, // 10 minutes - cost doesn't change often
  });
}

// ================================
// PROJECT CREATION ANALYTICS
// ================================

/**
 * Track project creation analytics and events
 */
export function useProjectCreationAnalytics() {
  const trackProjectCreationStarted = useCallback(() => {
    // TODO: Implement analytics tracking
  }, []);

  const trackProjectCreationCompleted = useCallback(
    (_projectId: string, _subscriptionId: string) => {
      // TODO: Implement analytics tracking
    },
    [],
  );

  const trackProjectCreationAbandoned = useCallback((_step: string) => {
    // TODO: Implement analytics tracking
  }, []);

  const trackPaymentInitiated = useCallback(
    (_projectId: string, _amount: number) => {
      // TODO: Implement analytics tracking
    },
    [],
  );

  return {
    trackProjectCreationStarted,
    trackProjectCreationCompleted,
    trackProjectCreationAbandoned,
    trackPaymentInitiated,
  };
}

// ================================
// PMA SUBSCRIPTION CREATION AFTER CERTIFICATES
// ================================

/**
 * Create PMA subscriptions for newly created PMA certificates
 */
export function useCreatePmaSubscriptionsForCertificates() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useMutation({
    mutationFn: async (params: {
      pmaCertificateIds: string[];
      projectId: string;
    }): Promise<PmaSubscriptionWithDetails[]> => {
      if (!contractorId) {
        throw new Error('Contractor ID not found');
      }

      const { pmaCertificateIds, projectId: _projectId } = params;
      const createdSubscriptions: PmaSubscriptionWithDetails[] = [];

      // Create subscription for each PMA certificate
      for (const pmaId of pmaCertificateIds) {
        const subscriptionResult = await pmaSubscriptionsService.create({
          pmaId,
          contractorId,
          status: 'pending_payment', // Always start with pending payment
        });

        if (subscriptionResult.error || !subscriptionResult.data) {
          throw new Error(
            `Failed to create PMA subscription for certificate ${pmaId}: ${subscriptionResult.error}`,
          );
        }

        // Get the full subscription details
        const fullSubscriptionResult = await pmaSubscriptionsService.getById(
          subscriptionResult.data.id,
        );

        if (fullSubscriptionResult.data) {
          createdSubscriptions.push(fullSubscriptionResult.data);
        }
      }

      return createdSubscriptions;
    },
    onSuccess: (pmaSubscriptions, variables) => {
      const { projectId } = variables;

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['pma-subscriptions', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['pma-access', projectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['projects', contractorId, user?.id],
      });

      const pmaCount = pmaSubscriptions.length;
      toast({
        title: 'PMA Subscriptions Created',
        description: `${pmaCount} PMA subscription(s) created successfully. Complete payments to activate PMA access.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Create PMA Subscriptions',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Auto-create PMA subscriptions when PMA certificates are created
 */
export function useAutoCreatePmaSubscriptions(projectId: string) {
  const createPmaSubscriptions = useCreatePmaSubscriptionsForCertificates();

  const createSubscriptionsForCertificates = useCallback(
    async (pmaCertificateIds: string[]) => {
      if (pmaCertificateIds.length === 0) return;

      await createPmaSubscriptions.mutateAsync({
        pmaCertificateIds,
        projectId,
      });
    },
    [createPmaSubscriptions, projectId],
  );

  return {
    createSubscriptionsForCertificates,
    isCreating: createPmaSubscriptions.isPending,
    error: createPmaSubscriptions.error,
  };
}

/**
 * Create PMA subscriptions for existing PMA certificates that don't have subscriptions
 */
export function useCreateMissingPmaSubscriptions() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useMutation({
    mutationFn: async (
      projectId: string,
    ): Promise<PmaSubscriptionWithDetails[]> => {
      if (!contractorId) {
        throw new Error('Contractor ID not found');
      }

      // Get all PMA certificates for this project
      const pmaCertificates = await getPMACertificateIds(projectId);

      if (!pmaCertificates || pmaCertificates.length === 0) {
        return [];
      }

      // Get existing PMA subscriptions for these certificates
      const { data: existingSubscriptions } =
        await pmaSubscriptionsService.getExistingByCertificateIds(
          pmaCertificates.map((cert) => cert.id),
        );

      const existingSubIds = new Set(
        existingSubscriptions?.map((sub) => sub.pma_certificate_id) || [],
      );

      // Find certificates without subscriptions
      const certificatesNeedingSubscriptions = pmaCertificates.filter(
        (cert) => !existingSubIds.has(cert.id),
      );

      if (certificatesNeedingSubscriptions.length === 0) {
        return [];
      }

      const createdSubscriptions: PmaSubscriptionWithDetails[] = [];

      // Create subscription for each certificate that doesn't have one
      for (const cert of certificatesNeedingSubscriptions) {
        const subscriptionResult = await pmaSubscriptionsService.create({
          pmaId: cert.id,
          contractorId,
          status: 'pending_payment',
        });

        if (subscriptionResult.error || !subscriptionResult.data) {
          throw new Error(
            `Failed to create PMA subscription for certificate ${cert.id}: ${subscriptionResult.error}`,
          );
        }

        // Get the full subscription details
        const fullSubscriptionResult = await pmaSubscriptionsService.getById(
          subscriptionResult.data.id,
        );

        if (fullSubscriptionResult.data) {
          createdSubscriptions.push(fullSubscriptionResult.data);
        }
      }

      return createdSubscriptions;
    },
    onSuccess: (pmaSubscriptions, projectId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['pma-subscriptions', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['pma-access', projectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['projects', contractorId, user?.id],
      });

      const pmaCount = pmaSubscriptions.length;
      if (pmaCount > 0) {
        toast({
          title: 'PMA Subscriptions Created',
          description: `${pmaCount} missing PMA subscription(s) created successfully. Complete payments to activate PMA access.`,
        });
      }
    },
    onError: (error) => {
      toast({
        title: 'Failed to Create Missing PMA Subscriptions',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Complete project creation flow: Project → PMA Certificates → PMA Subscriptions
 * This ensures the automatic flow you want: Project Creation → PMA Cert Creation → PMA Subscription Creation
 */
export function useCompleteProjectCreationFlow() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;
  const invalidateProjectAccess = useInvalidateProjectAccess();

  return useMutation({
    mutationFn: async (params: {
      projectData: ProjectFormData;
    }): Promise<ProjectCreationResult> => {
      if (!user || !contractorId) {
        throw new Error('User not authenticated or missing contractor profile');
      }

      const { projectData } = params;

      // Step 1: Create project with billing
      const projectResponse = await fetch('/api/projects/create-with-billing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: projectData.name,
          code: projectData.code,
          location: projectData.location,
          // Only send basic project data, PMA certs will be created separately
        }),
      });

      if (!projectResponse.ok) {
        const errorData = await projectResponse.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const projectResult = await projectResponse.json();
      const project = projectResult.project;

      // Step 2: Create PMA certificates (if provided in the form)
      const createdPmaSubscriptions: PmaSubscriptionWithDetails[] = [];

      if (projectData.pmas?.pmas && projectData.pmas.pmas.length > 0) {
        // First create competent persons if needed
        const competentPersonIds: string[] = [];

        if (projectData.competentPersons?.competent_persons) {
          for (const cpData of projectData.competentPersons.competent_persons) {
            // Create competent person via service
            const competentPerson = await createCompetentPerson({
              contractor_id: contractorId,
              name: cpData.name,
              phone_no: cpData.phone_number,
              ic_no: cpData.email, // Temporary mapping
              cp_type: 'CP1' as const,
            });

            competentPersonIds.push(competentPerson.id);
          }
        }

        // Then create PMA certificates
        for (let i = 0; i < projectData.pmas.pmas.length; i++) {
          const pmaData = projectData.pmas.pmas[i];

          // Step 2a: Create PMA certificate
          const pmaCertificate = await createPMACertificateForProject({
            pma_number: pmaData.pma_number,
            expiry_date: pmaData.expiry_date,
            location: pmaData.location,
            project_id: project.id,
            competent_person_id: competentPersonIds[i] || competentPersonIds[0],
            status: 'validating',
            // File handling would be done separately
          });

          // Step 3: Auto-create PMA subscription (1:1 with certificate)
          const subscriptionResult = await pmaSubscriptionsService.create({
            pmaId: pmaCertificate.id,
            contractorId,
            status: 'pending_payment',
          });

          if (subscriptionResult.error || !subscriptionResult.data) {
            throw new Error(
              `Failed to create PMA subscription: ${subscriptionResult.error}`,
            );
          }

          // Get full subscription details
          const fullSubscriptionResult = await pmaSubscriptionsService.getById(
            subscriptionResult.data.id,
          );

          if (fullSubscriptionResult.data) {
            createdPmaSubscriptions.push(fullSubscriptionResult.data);
          }
        }
      }

      return {
        project: project as ProjectWithBilling,
        pmaSubscriptions: createdPmaSubscriptions,
        nextSteps: [
          'Complete payment to activate PMA access for each certificate',
          'Set up project team members',
          'Configure project maintenance schedules',
          'Upload initial project documents',
        ],
      };
    },
    onSuccess: (data) => {
      const { project, pmaSubscriptions } = data;

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['projects', contractorId, user?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['projects', 'stats', contractorId, user?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['pma-subscriptions', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['pma-access', project.id],
      });

      // Invalidate project access
      invalidateProjectAccess(project.id, contractorId || undefined, user?.id);

      const pmaCount = pmaSubscriptions.length;
      const description =
        pmaCount > 0
          ? `Project "${project.name}" created with ${pmaCount} PMA certificate(s) and subscriptions. Complete payments to activate PMA access.`
          : `Project "${project.name}" created successfully. Add PMA certificates to enable billing.`;

      toast({
        title: 'Project Creation Complete',
        description,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Complete Project Creation',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}
