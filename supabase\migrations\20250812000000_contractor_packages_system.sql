-- ================================
-- CONTRACTOR PACKAGES SYSTEM
-- Complete package system setup for contractor pricing
-- ================================

-- Create contractors_package table
CREATE TABLE contractors_package (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  pricing numeric(10, 2) NOT NULL CHECK (pricing > 0),
  description text,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Add package_id column to contractors table
ALTER TABLE contractors 
ADD COLUMN package_id uuid REFERENCES contractors_package(id);

-- Create default "Standard Package" with RM150 pricing
INSERT INTO contractors_package (name, pricing, description) 
VALUES ('Standard Package', 150.00, 'Default package with standard monthly pricing');

-- Update all existing contractors to use the default package
UPDATE contractors 
SET package_id = (SELECT id FROM contractors_package WHERE name = 'Standard Package' LIMIT 1)
WHERE package_id IS NULL;

-- Create function to get contractor pricing
CREATE OR REPLACE FUNCTION get_contractor_pricing(contractor_id_param uuid)
RETURNS numeric(10, 2)
LANGUAGE plpgsql
AS $$
DECLARE
    package_pricing numeric(10, 2);
BEGIN
    -- Get pricing from contractor's package
    SELECT cp.pricing 
    INTO package_pricing
    FROM contractors c
    JOIN contractors_package cp ON c.package_id = cp.id
    WHERE c.id = contractor_id_param 
    AND cp.is_active = true;
    
    -- Return package pricing or default to 150.00
    RETURN COALESCE(package_pricing, 150.00);
END;
$$;

-- Create function to assign default package to new contractors
CREATE OR REPLACE FUNCTION assign_default_package_to_contractor()
RETURNS TRIGGER AS $$
BEGIN
    -- Only assign if no package is already specified
    IF NEW.package_id IS NULL THEN
        -- Assign the Standard Package to new contractors
        NEW.package_id = (
            SELECT id 
            FROM contractors_package 
            WHERE name = 'Standard Package' 
            AND is_active = true 
            LIMIT 1
        );
        
        -- If Standard Package doesn't exist for some reason, log a warning
        -- but allow the contractor creation to proceed
        IF NEW.package_id IS NULL THEN
            RAISE WARNING 'Standard Package not found - contractor created without package assignment';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically assign default package on contractor creation
CREATE TRIGGER trigger_assign_default_package
    BEFORE INSERT ON contractors
    FOR EACH ROW
    EXECUTE FUNCTION assign_default_package_to_contractor();

-- Create trigger function to update updated_at
CREATE OR REPLACE FUNCTION update_contractors_package_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for contractors_package
CREATE TRIGGER trigger_update_contractors_package_updated_at
    BEFORE UPDATE ON contractors_package
    FOR EACH ROW
    EXECUTE FUNCTION update_contractors_package_updated_at();

-- Create indexes for performance
CREATE INDEX idx_contractors_package_is_active ON contractors_package(is_active);
CREATE INDEX idx_contractors_package_pricing ON contractors_package(pricing);
CREATE INDEX idx_contractors_package_id ON contractors(package_id);

-- Add comments
COMMENT ON FUNCTION get_contractor_pricing(uuid) IS 'Returns the pricing for a contractor based on their package, or 150.00 as default';
COMMENT ON TRIGGER trigger_assign_default_package ON contractors IS 'Automatically assigns Standard Package to new contractors on creation';