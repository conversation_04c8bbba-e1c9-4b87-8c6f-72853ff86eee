'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSubscriptionReminders } from '@/features/dashboard/hooks/use-subscription-reminders';
import { AlertTriangle, Clock, Shield } from 'lucide-react';
import { useMemo } from 'react';

export function TrialExpirationReminder() {
  const { data: reminderData, isLoading } = useSubscriptionReminders();

  const allTrialReminders = useMemo(() => {
    if (!reminderData) return [];

    const now = new Date();

    return reminderData.trialSubscriptions
      .filter((sub) => sub.trial_ends_at && sub.status === 'trial')
      .map((sub) => {
        const trialEndDate = new Date(sub.trial_ends_at!);
        const timeDiff = trialEndDate.getTime() - now.getTime();
        const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

        return {
          ...sub,
          daysLeft,
          isExpired: daysLeft <= 0,
          isExpiringSoon: daysLeft > 0 && daysLeft <= 3,
          isExpiringSoonish: daysLeft > 3 && daysLeft <= 7,
        };
      })
      .sort((a, b) => a.daysLeft - b.daysLeft); // Sort by days left (urgent first)
  }, [reminderData]);

  if (isLoading) {
    return (
      <div className="h-20 flex items-center justify-center">
        <div className="animate-spin w-6 h-6 border-2 border-blue-200 border-t-blue-600 rounded-full"></div>
      </div>
    );
  }

  // Show if there are any trial subscriptions at all
  if (!reminderData || allTrialReminders.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      {allTrialReminders.map((trial) => {
        const getAlertStyle = () => {
          if (trial.isExpired) return 'border-red-200 bg-red-50';
          if (trial.isExpiringSoon) return 'border-amber-200 bg-amber-50';
          return 'border-blue-200 bg-blue-50';
        };

        const getTextStyle = () => {
          if (trial.isExpired) return 'text-red-800';
          if (trial.isExpiringSoon) return 'text-amber-800';
          return 'text-blue-800';
        };

        const getIcon = () => {
          if (trial.isExpired)
            return <AlertTriangle className="h-4 w-4 text-red-600" />;
          if (trial.isExpiringSoon)
            return <Clock className="h-4 w-4 text-amber-600" />;
          return <Shield className="h-4 w-4 text-blue-600" />;
        };

        const getMessage = () => {
          if (trial.isExpired) return 'Your trial has expired';
          if (trial.daysLeft === 1) return 'Your trial expires in 1 day';
          return `Your trial expires in ${trial.daysLeft} days`;
        };

        return (
          <Alert key={trial.id} className={getAlertStyle()}>
            {getIcon()}
            <AlertDescription className={getTextStyle()}>
              {getMessage()}
            </AlertDescription>
          </Alert>
        );
      })}
    </div>
  );
}
