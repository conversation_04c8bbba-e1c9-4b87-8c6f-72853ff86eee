import {
  WebhookService,
  billPlzWebhookSchema,
} from '@/features/billing/services/webhook.service';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

export async function POST(request: NextRequest) {
  try {
    // Get raw body for parsing
    const rawBody = await request.text();

    // Parse webhook payload first (BillPlz sends form-encoded data, not JSON)
    let webhookData: Record<string, unknown>;
    let signaturePayload: Record<string, string>; // For signature verification
    let signature: string | null = null;
    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('application/x-www-form-urlencoded')) {
      const urlParams = new URLSearchParams(rawBody);

      // Create payload for signature verification (preserve original string values)
      signaturePayload = Object.fromEntries(urlParams.entries());

      // Extract signature from payload (BillPlz sends it in the body, not header)
      signature = signaturePayload.x_signature || null;

      // Remove x_signature from payload before verification (it shouldn't be part of the signed data)
      const { x_signature: _x_signature, ...payloadForSignature } =
        signaturePayload;
      signaturePayload = payloadForSignature;

      // Create payload for processing (with type conversions)
      webhookData = { ...signaturePayload };

      // Convert boolean strings to actual booleans for processing
      if (webhookData.paid) {
        webhookData.paid = webhookData.paid === 'true';
      }

      // Convert amount to number if present for processing
      if (webhookData.amount && typeof webhookData.amount === 'string') {
        webhookData.amount = parseInt(webhookData.amount, 10);
      }
      if (
        webhookData.paid_amount &&
        typeof webhookData.paid_amount === 'string'
      ) {
        webhookData.paid_amount = parseInt(webhookData.paid_amount, 10);
      }

      // Remove x_signature from processing data if it somehow got included
      delete webhookData.x_signature;
    } else {
      // For JSON payloads, use the same object for both
      const parsedBody = JSON.parse(rawBody);
      signature = parsedBody.x_signature || request.headers.get('x-signature');

      // Remove x_signature from payload before verification
      const { x_signature: _x_signature2, ...payloadForSignature } = parsedBody;
      webhookData = payloadForSignature;
      signaturePayload = payloadForSignature;
    }

    // Verify BillPlz webhook signature using original string values
    if (
      !WebhookService.verifyBillPlzSignature(
        signaturePayload,
        signature,
        rawBody,
      )
    ) {
      console.error('Invalid BillPlz webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const validatedData = billPlzWebhookSchema.parse(webhookData);

    // Process webhook using the service
    const result = await WebhookService.processBillPlzWebhook(validatedData);

    if (!result.success) {
      console.error('Webhook processing failed:', result.error);
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully',
      data: {
        paymentRecordId: result.paymentRecordId,
        subscriptionId: result.subscriptionId,
        projectId: result.projectId,
        contractorId: result.contractorId,
        billState: result.billState,
        accessRestored: result.accessRestored,
        processedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('BillPlz webhook validation error:', error.errors);
      return NextResponse.json(
        {
          error: 'Invalid webhook payload',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('BillPlz webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 },
    );
  }
}
