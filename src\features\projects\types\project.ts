import { Database } from '@/types/database';

// Database types
export type Project = Database['public']['Tables']['projects']['Row'] & {
  agency?: Database['public']['Tables']['agencies']['Row'] | null;
  project_users?: Array<{
    id: string;
    role: Database['public']['Tables']['project_users']['Row']['role'];
    status: Database['public']['Tables']['project_users']['Row']['status'];
    assigned_date: string | null;
    is_active: boolean | null;
    user: Database['public']['Tables']['users']['Row'];
  }> | null;
};

// Project User relationship types
export type ProjectUser = Database['public']['Tables']['project_users']['Row'];
export type ProjectUserInsert =
  Database['public']['Tables']['project_users']['Insert'];
export type ProjectUserUpdate =
  Database['public']['Tables']['project_users']['Update'];
export type ProjectInsert = Database['public']['Tables']['projects']['Insert'];
export type ProjectUpdate = Database['public']['Tables']['projects']['Update'];

// Agency types
export type Agency = Database['public']['Tables']['agencies']['Row'];
export type AgencyInsert = Database['public']['Tables']['agencies']['Insert'];
export type AgencyUpdate = Database['public']['Tables']['agencies']['Update'];

// Form types for multi-step form
export interface ProjectDetailsFormData {
  name: string;
  code: string;
  agency_id?: string;
  agency_name?: string;
  jkr_pic_id: string; // Single JKR user ID
  user_ids: string[]; // Array of all user IDs to assign to project
  location: string;
  state?: string;
  start_date: string;
  end_date?: string;
  status: string;
}

// User assignment for projects
export interface ProjectUserAssignment {
  user_id: string;
  role: Database['public']['Tables']['project_users']['Row']['role'];
  assigned_date?: string;
}

export interface CompetentPersonFormData {
  name: string;
  phone_number: string;
  email: string;
  registration_cert_file?: File;
  lif_list_files?: File[];
}

export interface CompetentPersonsFormData {
  competent_persons: CompetentPersonFormData[];
}

export interface PmaFormData {
  pma_number: string;
  expiry_date: string;
  inspection_date: string;
  lift_installation_date: string;
  location: string;
  competent_person_id: string;
  file: File;
}

export interface PmasFormData {
  pmas: PmaFormData[];
}

export interface ProjectFormData {
  // Project details
  name: string;
  code: string;
  agency_id: string;
  jkr_pic_id: string; // Single JKR user ID
  location: string;
  state?: string;
  start_date: string;
  end_date?: string;
  status: string;
  // Competent persons
  competentPersons: CompetentPersonsFormData;
  // PMAs
  pmas: PmasFormData;
}

// Basic project form data for simple forms (without multi-step requirements)
export interface BasicProjectFormData {
  name: string;
  code: string;
  location: string;
  start_date: string;
  end_date: string;
  status: string;
}

// Status types
export type ProjectStatus = 'pending' | 'active' | 'completed' | 'cancelled';

// Statistics types
export interface ProjectStats {
  total: number;
  active: number;
  pending: number;
  completed: number;
}

// UI component props
export interface ProjectCardProps {
  project: Project;
  onEdit?: (project: Project) => void;
  onDelete?: (projectId: string) => void;
}

export interface ProjectListProps {
  projects: Project[];
  isLoading?: boolean;
  onProjectClick?: (project: Project) => void;
}

export interface ProjectFormProps {
  initialData?: Partial<ProjectFormData>;
  onSubmit: (data: ProjectFormData) => Promise<{ id: string; code: string }>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export interface BasicProjectFormProps {
  initialData?: Partial<BasicProjectFormData>;
  onSubmit: (data: BasicProjectFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

// Multi-step form props
export interface ProjectDetailsStepProps {
  data: Partial<ProjectDetailsFormData>;
  onNext: (data: ProjectDetailsFormData) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}
