'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { AlertCircle, Download, Eye, FileText, Upload, X } from 'lucide-react';
import Image from 'next/image';
import * as React from 'react';

interface FileItem {
  file: File;
  preview?: string; // Object URL for preview
}

interface FileUploadWithPreviewProps {
  onFilesChange: (files: File[]) => void;
  accept?: string;
  maxSize?: number; // in bytes
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  files?: File[]; // Controlled files prop
  onFileRemove?: (index: number) => void;
  allowPreview?: boolean; // New prop to enable preview functionality
}

export function FileUploadWithPreview({
  onFilesChange,
  accept = '.pdf,.jpg,.jpeg,.png',
  maxSize = 5 * 1024 * 1024, // 5MB
  maxFiles = 1,
  disabled = false,
  className,
  files = [],
  onFileRemove,
  allowPreview = true,
}: FileUploadWithPreviewProps) {
  const [dragActive, setDragActive] = React.useState(false);
  const [errors, setErrors] = React.useState<string[]>([]);
  const [previewFile, setPreviewFile] = React.useState<FileItem | null>(null);
  const [fileItems, setFileItems] = React.useState<FileItem[]>([]);
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Sync file items with controlled files prop
  React.useEffect(() => {
    setFileItems((prev) => {
      const newFileItems = files.map((file) => {
        const existingItem = prev.find(
          (item) =>
            item.file.name === file.name &&
            item.file.size === file.size &&
            item.file.lastModified === file.lastModified,
        );

        if (existingItem) {
          return existingItem;
        }

        // Create preview URL for images
        let preview: string | undefined;
        if (isImageFile(file)) {
          preview = URL.createObjectURL(file);
        }

        return { file, preview };
      });

      // Cleanup old preview URLs that are no longer needed
      prev.forEach((item) => {
        if (
          item.preview &&
          !newFileItems.find((newItem) => newItem.preview === item.preview)
        ) {
          URL.revokeObjectURL(item.preview);
        }
      });

      return newFileItems;
    });
  }, [files]);

  // Cleanup preview URLs on unmount
  React.useEffect(() => {
    return () => {
      fileItems.forEach((item) => {
        if (item.preview) {
          URL.revokeObjectURL(item.preview);
        }
      });
    };
  }, [fileItems]);

  const isImageFile = (file: File): boolean => {
    const imageTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
    ];
    return (
      imageTypes.includes(file.type) ||
      /\.(jpg|jpeg|png|gif|webp)$/i.test(file.name)
    );
  };

  const isPdfFile = (file: File): boolean => {
    return file.type === 'application/pdf' || /\.pdf$/i.test(file.name);
  };

  const getFileTypeLabel = (file: File): string => {
    if (isImageFile(file)) return 'Image file';
    if (isPdfFile(file)) return 'PDF file';
    return 'File';
  };

  const handleDrag = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const validateFile = React.useCallback(
    (file: File): string | null => {
      if (file.size > maxSize) {
        return `File "${file.name}" is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB.`;
      }

      const acceptedTypes = accept.split(',').map((type) => type.trim());
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      const mimeType = file.type;

      const isValidType = acceptedTypes.some((type) => {
        if (type.startsWith('.')) {
          return fileExtension === type;
        }
        return mimeType.includes(type.replace('*', ''));
      });

      if (!isValidType) {
        return `File "${file.name}" has an invalid type. Accepted types: ${accept}`;
      }

      return null;
    },
    [maxSize, accept],
  );

  const handleFiles = React.useCallback(
    (newFiles: FileList | File[]) => {
      const fileArray = Array.from(newFiles);
      const newErrors: string[] = [];

      // Validate each file
      const validFiles: File[] = [];
      fileArray.forEach((file) => {
        const error = validateFile(file);
        if (error) {
          newErrors.push(error);
        } else {
          validFiles.push(file);
        }
      });

      // Check total file count
      const totalFiles = files.length + validFiles.length;
      if (totalFiles > maxFiles) {
        if (validFiles.length > 0 && files.length > 0) {
          newErrors.push(`Maximum ${maxFiles} file(s) allowed.`);
        }
        const allowedCount = maxFiles - files.length;
        validFiles.splice(allowedCount);
      }

      setErrors(newErrors);

      if (validFiles.length > 0) {
        const finalFiles =
          maxFiles === 1 ? validFiles : [...files, ...validFiles];
        onFilesChange(finalFiles);
      }

      if (validFiles.length === 0 && newErrors.length === 0) {
        setErrors([]);
      }
    },
    [files, maxFiles, onFilesChange, validateFile],
  );

  const handleDrop = React.useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (disabled) return;

      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles && droppedFiles.length > 0) {
        handleFiles(droppedFiles);
      }
    },
    [handleFiles, disabled],
  );

  const handleInputChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      e.preventDefault();
      if (disabled) return;

      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        handleFiles(selectedFiles);
      }

      // Reset the input value to allow selecting the same file again
      if (e.target) {
        e.target.value = '';
      }
    },
    [handleFiles, disabled],
  );

  const openFileDialog = React.useCallback(
    (e?: React.MouseEvent) => {
      if (disabled) return;
      e?.preventDefault();
      e?.stopPropagation();
      inputRef.current?.click();
    },
    [disabled],
  );

  const removeFile = React.useCallback(
    (index: number) => {
      if (disabled) return;

      const updatedFiles = [...files];
      updatedFiles.splice(index, 1);
      onFilesChange(updatedFiles);

      // Clear errors when removing files
      setErrors([]);

      // Call custom remove handler if provided
      onFileRemove?.(index);
    },
    [files, onFilesChange, disabled, onFileRemove],
  );

  const handlePreview = React.useCallback(
    (fileItem: FileItem, e?: React.MouseEvent) => {
      e?.preventDefault();
      e?.stopPropagation();
      if (!allowPreview) return;
      setPreviewFile(fileItem);
    },
    [allowPreview],
  );

  const downloadFile = (file: File) => {
    const url = URL.createObjectURL(file);
    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      <div className={cn('w-full space-y-3', className)}>
        {/* Drop Zone - Only show if no files or can upload more */}
        {(!files || files.length === 0 || files.length < maxFiles) && (
          <div
            className={cn(
              'relative border-2 border-dashed rounded-lg p-6 transition-all duration-200 group',
              dragActive
                ? 'border-primary bg-primary/5 scale-[1.01]'
                : 'border-border hover:border-primary/60 hover:bg-muted/30',
              disabled && 'opacity-50 cursor-not-allowed',
              !disabled && 'cursor-pointer',
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={openFileDialog}
          >
            <input
              ref={inputRef}
              type="file"
              multiple={maxFiles > 1}
              accept={accept}
              onChange={handleInputChange}
              disabled={disabled}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              onClick={(e) => e.stopPropagation()}
            />

            <div className="flex flex-col items-center justify-center space-y-3 min-h-[80px]">
              <div
                className={cn(
                  'p-3 rounded-full transition-all duration-200',
                  dragActive ? 'bg-primary/15' : 'bg-primary/10',
                )}
              >
                <Upload
                  className={cn(
                    'w-5 h-5 transition-all duration-200',
                    dragActive
                      ? 'text-primary'
                      : 'text-primary/70 group-hover:text-primary',
                  )}
                />
              </div>

              <div className="text-center space-y-1">
                <p className="text-sm font-medium text-foreground">
                  Drop {maxFiles === 1 ? 'file' : 'files'} here or{' '}
                  <span className="text-primary underline underline-offset-2">
                    browse
                  </span>
                </p>
                <p className="text-xs text-muted-foreground">
                  {accept.replace(/\./g, '').toUpperCase()} • Max{' '}
                  {Math.round(maxSize / 1024 / 1024)}MB
                  {maxFiles > 1 && ` • Up to ${maxFiles} files`}
                  {allowPreview && ' • Click eye icon to preview'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Uploaded Files Display */}
        {fileItems.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-foreground">
              Selected Files {allowPreview && '(Click to preview)'}
            </h4>
            <div className="space-y-2">
              {fileItems.map((fileItem, index) => (
                <div
                  key={`${fileItem.file.name}-${index}`}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg border border-border hover:bg-muted/70 transition-colors"
                >
                  <div className="flex items-center space-x-3 min-w-0 flex-1">
                    <div className="flex-shrink-0">
                      <FileText className="w-5 h-5 text-primary" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-foreground truncate">
                        {fileItem.file.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {getFileTypeLabel(fileItem.file)} •{' '}
                        {formatFileSize(fileItem.file.size)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1">
                    {allowPreview && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={(e) => handlePreview(fileItem, e)}
                        disabled={disabled}
                        className="h-8 w-8 p-0 hover:bg-primary/10"
                        title="Preview file"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    )}

                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        downloadFile(fileItem.file);
                      }}
                      disabled={disabled}
                      className="h-8 w-8 p-0 hover:bg-primary/10"
                      title="Download file"
                    >
                      <Download className="w-4 h-4" />
                    </Button>

                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeFile(index);
                      }}
                      disabled={disabled}
                      className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                      title="Remove file"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Error Messages */}
        {errors.length > 0 && (
          <div className="space-y-1">
            {errors.map((error, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 text-xs text-destructive bg-destructive/5 p-2 rounded-md border border-destructive/20"
              >
                <AlertCircle className="w-3 h-3 flex-shrink-0" />
                <span>{error}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Preview Modal */}
      {allowPreview && (
        <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
          <DialogContent className="max-w-4xl max-h-[95vh] p-0">
            <DialogHeader className="sr-only">
              <DialogTitle>
                File Preview - {previewFile?.file.name || 'Unknown file'}
              </DialogTitle>
            </DialogHeader>

            <div className="relative h-full">
              {/* Header with controls */}
              <div className="absolute top-0 right-0 z-10 flex items-center gap-2 p-4 bg-white/90 backdrop-blur-sm rounded-bl-lg">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => previewFile && downloadFile(previewFile.file)}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPreviewFile(null)}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Close
                </Button>
              </div>

              {/* File Content */}
              <div className="h-[90vh] w-full">
                {previewFile && (
                  <>
                    {isImageFile(previewFile.file) ? (
                      <div className="h-full flex items-center justify-center bg-gray-50 p-4">
                        {previewFile.preview ? (
                          <Image
                            src={previewFile.preview}
                            alt={previewFile.file.name}
                            width={800}
                            height={600}
                            className="max-w-full max-h-full object-contain rounded-lg"
                            onError={() => {
                              console.error('Failed to load image preview');
                            }}
                          />
                        ) : (
                          <div className="text-center">
                            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-600">
                              Could not generate image preview
                            </p>
                          </div>
                        )}
                      </div>
                    ) : isPdfFile(previewFile.file) ? (
                      <div className="h-full w-full">
                        <iframe
                          src={URL.createObjectURL(previewFile.file)}
                          className="w-full h-full border-0"
                          title="PDF Preview"
                        />
                      </div>
                    ) : (
                      <div className="h-full flex items-center justify-center bg-gray-50">
                        <div className="text-center max-w-md">
                          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 mb-4">
                            Preview not available for this file type
                          </p>
                          <p className="text-sm text-gray-500 mb-6">
                            File: {previewFile.file.name}
                          </p>
                          <Button
                            onClick={() => downloadFile(previewFile.file)}
                            className="flex items-center gap-2"
                          >
                            <Download className="h-4 w-4" />
                            Download File
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
