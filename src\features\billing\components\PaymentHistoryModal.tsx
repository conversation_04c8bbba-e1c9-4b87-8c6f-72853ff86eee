'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  CreditCard,
  ExternalLink,
  FileText,
  Package,
  Receipt,
  RefreshCw,
  Search,
} from 'lucide-react';
import { useState } from 'react';
import { useBatchedPayments } from '../hooks/useBatchedPayments';
import type { PaymentStatus } from '../types/payment-records';

export interface PaymentHistoryModalProps {
  contractorId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PaymentHistoryModal({
  contractorId,
  open,
  onOpenChange,
}: PaymentHistoryModalProps) {
  // Remove unused translation hook for now

  const [filters, setFilters] = useState({
    status: '' as PaymentStatus | '',
    dateFrom: '',
    dateTo: '',
    search: '',
  });

  const [page, setPage] = useState(1);
  const limit = 20;

  // Fetch batched payments only
  const {
    data: batchedData,
    isLoading,
    error,
    refetch,
  } = useBatchedPayments({
    contractorId,
    status: filters.status || undefined,
    dateFrom: filters.dateFrom || undefined,
    dateTo: filters.dateTo || undefined,
    page,
    limit,
    enabled: open && !!contractorId,
  });

  // Filter batched payments by search term (client-side)
  const filteredBatches =
    batchedData?.batches?.filter((batch) => {
      if (!filters.search) return true;

      const searchTerm = filters.search.toLowerCase();
      return (
        batch.pma_numbers.some((pmaNumber) =>
          pmaNumber.toLowerCase().includes(searchTerm),
        ) ||
        batch.batch_bill_id?.toLowerCase().includes(searchTerm) ||
        batch.id.toLowerCase().includes(searchTerm)
      );
    }) || [];

  const getTotalRecordsCount = () => filteredBatches.length;
  const getTotalCount = () => batchedData?.total || 0;

  const getStatusBadge = (status: PaymentStatus) => {
    const statusConfig = {
      paid: {
        variant: 'default' as const,
        color: 'bg-green-100 text-green-800',
      },
      pending: {
        variant: 'secondary' as const,
        color: 'bg-yellow-100 text-yellow-800',
      },
      failed: {
        variant: 'destructive' as const,
        color: 'bg-red-100 text-red-800',
      },
      cancelled: {
        variant: 'outline' as const,
        color: 'bg-gray-100 text-gray-800',
      },
      refunded: {
        variant: 'outline' as const,
        color: 'bg-blue-100 text-blue-800',
      },
    } as const;

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <Badge variant={config.variant} className={config.color}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('en-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment History
          </DialogTitle>
          <DialogDescription>
            View batched payment records and transaction history for your
            projects
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Filters */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div>
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    status: value === 'all' ? '' : (value as PaymentStatus),
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="date-from">Date From</Label>
              <Input
                id="date-from"
                type="date"
                value={filters.dateFrom}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, dateFrom: e.target.value }))
                }
              />
            </div>

            <div>
              <Label htmlFor="date-to">Date To</Label>
              <Input
                id="date-to"
                type="date"
                value={filters.dateTo}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, dateTo: e.target.value }))
                }
              />
            </div>

            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="PMA Number, Bill ID..."
                  className="pl-10"
                  value={filters.search}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, search: e.target.value }))
                  }
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {isLoading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <span>
                  {getTotalRecordsCount()} of {getTotalCount()} batched payments
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* Batched Payments Table */}
          <div className="border rounded-lg overflow-hidden">
            <div className="max-h-[500px] overflow-y-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>PMAs</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Payment Method</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Skeleton className="h-4 w-24" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-32" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-20" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-6 w-16" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-20" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-24" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-8 w-8" />
                        </TableCell>
                      </TableRow>
                    ))
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-red-600">
                          Failed to load batched payments. Please try again.
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredBatches.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-muted-foreground">
                          No batched payments found
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredBatches.map((batch) => (
                      <TableRow key={batch.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">
                              {formatDate(batch.created_at)}
                            </div>
                            {batch.paid_at && (
                              <div className="text-xs text-muted-foreground">
                                Paid: {formatDate(batch.paid_at)}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1">
                              <Package className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">
                                {batch.total_pma_count} PMAs
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {formatCurrency(batch.total_amount * 100)}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(batch.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">Billplz</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {batch.batch_bill_id ? (
                              <div className="font-mono text-xs">
                                {batch.batch_bill_id}
                              </div>
                            ) : (
                              <div className="text-xs text-muted-foreground">
                                No reference
                              </div>
                            )}
                            {batch.failure_reason && (
                              <div className="text-xs text-red-600">
                                {batch.failure_reason}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                window.open(
                                  `/billing/batch-invoice/${batch.id}`,
                                  '_blank',
                                );
                              }}
                              title="View Batch Invoice"
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                            {batch.status === 'paid' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  window.open(
                                    `/billing/batch-receipt/${batch.id}`,
                                    '_blank',
                                  );
                                }}
                                title="View Batch Receipt"
                              >
                                <Receipt className="h-4 w-4" />
                              </Button>
                            )}
                            {batch.batch_bill_id && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  // TODO: Open bill details or redirect to Billplz
                                }}
                                title="View Bill Details"
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Pagination */}
          {batchedData && batchedData.total > limit && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Page {page} of {Math.ceil(batchedData.total / limit)}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page <= 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={!batchedData.hasMore}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
