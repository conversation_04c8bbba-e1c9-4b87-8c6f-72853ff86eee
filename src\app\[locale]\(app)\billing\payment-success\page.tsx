'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, ArrowLeft, Clock } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState, Suspense } from 'react';

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const t = useTranslations('billing.paymentSuccess');
  const [isProcessing, setIsProcessing] = useState(true);

  // Parse BillPlz redirect parameters
  const billplzId = searchParams.get('billplz[id]');
  const billplzPaid = searchParams.get('billplz[paid]');
  const billplzPaidAt = searchParams.get('billplz[paid_at]');
  const billplzSignature = searchParams.get('billplz[x_signature]');

  // Format payment timestamp
  const formatPaymentTime = (timestamp: string | null) => {
    if (!timestamp) return 'N/A';
    try {
      return new Date(timestamp.replace(' ', 'T')).toLocaleString('en-MY', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Kuala_Lumpur',
      });
    } catch {
      return timestamp;
    }
  };

  // Simulate processing delay (webhook might take a moment)
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsProcessing(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const isPaid = billplzPaid === 'true';

  return (
    <div className="container mx-auto max-w-2xl py-8 px-4">
      <div className="text-center mb-8">
        {isPaid ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-green-600">{t('title')}</h1>
            <p className="text-muted-foreground">{t('subtitle')}</p>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-red-600" />
            </div>
            <h1 className="text-3xl font-bold text-red-600">
              {t('titleError')}
            </h1>
            <p className="text-muted-foreground">{t('subtitleError')}</p>
          </div>
        )}
      </div>

      {isPaid && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>{t('paymentDetails')}</span>
              {isProcessing && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Clock className="w-4 h-4 mr-1 animate-spin" />
                  {t('processing')}
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t('paymentId')}
                </label>
                <p className="font-mono text-sm bg-muted p-2 rounded">
                  {billplzId || 'N/A'}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t('paymentTime')}
                </label>
                <p className="text-sm bg-muted p-2 rounded">
                  {formatPaymentTime(billplzPaidAt)}
                </p>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t('status')}
              </label>
              <div className="flex items-center space-x-2 mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium text-green-600">
                  {t('paymentConfirmed')}
                </span>
              </div>
            </div>

            {isProcessing ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-blue-600 animate-spin" />
                  <span className="text-sm text-blue-800">
                    {t('processingSubscription')}
                  </span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  {t('processingNote')}
                </p>
              </div>
            ) : (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-800">
                    {t('subscriptionActivated')}
                  </span>
                </div>
                <p className="text-xs text-green-600 mt-1">
                  {t('accessGranted')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button asChild variant="default" size="lg">
          <Link href="/billing">{t('returnToBilling')}</Link>
        </Button>

        <Button asChild variant="outline" size="lg">
          <Link href="/dashboard">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('goToDashboard')}
          </Link>
        </Button>
      </div>

      {/* Debug information in development */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="mt-8 border-dashed">
          <CardHeader>
            <CardTitle className="text-sm">{t('debugInfo')}</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-muted p-2 rounded overflow-auto">
              {JSON.stringify(
                {
                  billplzId,
                  billplzPaid,
                  billplzPaidAt,
                  billplzSignature: billplzSignature ? '***hidden***' : null,
                },
                null,
                2,
              )}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function PaymentSuccessLoading() {
  return (
    <div className="container mx-auto max-w-2xl py-8 px-4">
      <div className="text-center mb-8">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <Clock className="w-10 h-10 text-gray-400 animate-spin" />
          </div>
          <h1 className="text-2xl font-bold text-gray-600">Loading...</h1>
          <p className="text-muted-foreground">
            Please wait while we process your payment details.
          </p>
        </div>
      </div>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<PaymentSuccessLoading />}>
      <PaymentSuccessContent />
    </Suspense>
  );
}
