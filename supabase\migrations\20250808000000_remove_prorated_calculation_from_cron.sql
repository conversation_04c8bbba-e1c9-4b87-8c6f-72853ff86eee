-- ================================
-- REMOVE PRO-RATED CALCULATION FROM CRON JOB
-- ================================
-- Migration: Remove calculated_amount update from daily cron job
-- since we now do real-time calculation in the payment service
-- Keep only the trial expiration logic

-- ================================
-- 1. UPDATE CRON FUNCTION
-- ================================

DROP FUNCTION IF EXISTS public.update_prorated_pma_amounts();

CREATE OR REPLACE FUNCTION public.handle_trial_expiration() 
RETURNS TABLE(
    trial_expired_count INTEGER,
    execution_time TIMESTAMPTZ,
    details TEXT
) AS $$
DECLARE
    trial_expiry_count INTEGER := 0;
    start_time TIMESTAMPTZ := CURRENT_TIMESTAMP;
BEGIN
    -- Handle trial period expiration (convert trial to pending_payment)
    UPDATE public.pma_subscriptions 
    SET 
        status = 'pending_payment',
        updated_at = CURRENT_TIMESTAMP
    WHERE status = 'trial' 
    AND trial_ends_at < CURRENT_TIMESTAMP;
    
    -- Get count of expired trials
    GET DIAGNOSTICS trial_expiry_count = ROW_COUNT;
    
    -- Return execution summary
    RETURN QUERY SELECT 
        trial_expiry_count,
        start_time,
        format('Expired %s trial subscriptions and converted to pending_payment.', trial_expiry_count);
END;
$$ LANGUAGE plpgsql;

-- Update function comment
COMMENT ON FUNCTION public.handle_trial_expiration IS 'Daily function to handle trial expiration by converting trial subscriptions to pending_payment status. Called by cron job at 12:00 AM. Pro-rate calculations are now handled in real-time during payment processing.';

-- ================================
-- 2. UPDATE CRON JOB SCHEDULE
-- ================================

-- Remove old cron job if it exists (ignore error if it doesn't exist)
DO $$
BEGIN
    PERFORM cron.unschedule('pma-prorated-daily-update');
EXCEPTION
    WHEN OTHERS THEN
        -- Job doesn't exist, continue
        NULL;
END;
$$;

-- Add new cron job for trial expiration only
SELECT cron.schedule(
    'handle-trial-expiration',
    '0 0 * * *', -- Daily at 12:00 AM
    $$SELECT public.handle_trial_expiration();$$
);

-- ================================
-- MIGRATION SUMMARY
-- ================================
-- This migration:
-- 1. Removes the calculated_amount update logic from the daily cron job
-- 2. Keeps the essential trial expiration logic
-- 3. Updates the cron schedule to use the new function
-- 4. Pro-rate calculations are now handled in real-time by BulkPaymentService
-- ================================