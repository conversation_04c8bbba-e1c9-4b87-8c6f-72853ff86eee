'use client';

// Backward compatibility exports for auth hooks
// This allows existing code to continue importing from @/hooks/use-auth
// while the actual hooks are now in the auth feature

export {
  useUser,
  useUserWithProfile,
  useLogin,
  useSignUp,
  useLogout,
  useSession,
  useAuthStateChange,
  usePasswordReset,
  useVerifyOtp,
} from '../features/auth/hooks/use-auth';

export { useUpdateProfile } from '../features/auth/hooks/use-profile';
