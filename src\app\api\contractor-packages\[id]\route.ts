import { ContractorPackageService } from '@/features/billing/services/contractor-package.service';
import { authenticateWithPermission } from '@/features/auth';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// Schema for updating contractor packages
const UpdatePackageSchema = z
  .object({
    name: z.string().min(1, 'Package name is required').optional(),
    pricing: z.number().positive('Pricing must be positive').optional(),
    description: z.string().optional(),
    is_active: z.boolean().optional(),
  })
  .refine(
    (data) => Object.keys(data).length > 0,
    'At least one field must be provided for update',
  );

/**
 * GET /api/contractor-packages/[id]
 * Get a specific contractor package
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id } = await params;
    const result = await ContractorPackageService.getById(id);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error?.includes('not found') ? 404 : 500 },
      );
    }

    // Non-admins can only see active packages
    if (user.user_role !== 'admin' && !result.data?.is_active) {
      return NextResponse.json({ error: 'Package not found' }, { status: 404 });
    }

    return NextResponse.json({ package: result.data });
  } catch (error) {
    console.error('Get contractor package error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * PUT /api/contractor-packages/[id]
 * Update a contractor package (admins only)
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await authenticateWithPermission('projects.create');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Only admins can update packages
    if (user.user_role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validation = UpdatePackageSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { id } = await params;
    const updates = validation.data;
    const result = await ContractorPackageService.update(id, updates);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error?.includes('not found') ? 404 : 500 },
      );
    }

    return NextResponse.json({
      message: 'Package updated successfully',
      package: result.data,
    });
  } catch (error) {
    console.error('Update contractor package error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/contractor-packages/[id]
 * Delete a contractor package (admins only)
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await authenticateWithPermission('projects.create');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Only admins can delete packages
    if (user.user_role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const result = await ContractorPackageService.delete(id);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error?.includes('not found') ? 404 : 400 },
      );
    }

    return NextResponse.json({
      message: 'Package deleted successfully',
    });
  } catch (error) {
    console.error('Delete contractor package error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
