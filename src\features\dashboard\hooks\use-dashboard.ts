'use client';

import { useProjectContext } from '@/providers/project-context';
import { useQuery } from '@tanstack/react-query';
import { DashboardService } from '../services/dashboard-service';

/**
 * Hook to get PMA certificates statistics
 * @returns PMA certificates statistics
 */
export function usePMACertificatesStats() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();

  return useQuery({
    queryKey: ['dashboard', 'pma-certificates', selectedProjectId],
    queryFn: async () => {
      return await DashboardService.getPMACertificatesStats(
        isInProjectContext && selectedProjectId ? selectedProjectId : undefined,
      );
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get maintenance logs statistics
 * @returns Maintenance logs statistics
 */
export function useMaintenanceLogsStats() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();

  return useQuery({
    queryKey: ['dashboard', 'maintenance-logs', selectedProjectId],
    queryFn: async () => {
      return await DashboardService.getMaintenanceLogsStats(
        isInProjectContext && selectedProjectId ? selectedProjectId : undefined,
      );
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get complaints statistics
 * @returns Complaints statistics
 */
export function useComplaintsStats() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();

  return useQuery({
    queryKey: ['dashboard', 'complaints', selectedProjectId],
    queryFn: async () => {
      return await DashboardService.getComplaintsStats(
        isInProjectContext && selectedProjectId ? selectedProjectId : undefined,
      );
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get safety incident statistics
 * @returns Safety incident statistics
 */
export function useSafetyIncidentStats() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();

  return useQuery({
    queryKey: ['dashboard', 'safety-incidents', selectedProjectId],
    queryFn: async () => {
      return await DashboardService.getSafetyIncidentStats(
        isInProjectContext && selectedProjectId ? selectedProjectId : undefined,
      );
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get compliance score
 * @returns Compliance score
 */
export function useComplianceScore() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();

  return useQuery({
    queryKey: ['dashboard', 'compliance', selectedProjectId],
    queryFn: async () => {
      return await DashboardService.getComplianceScore(
        isInProjectContext && selectedProjectId ? selectedProjectId : undefined,
      );
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get all dashboard statistics at once
 * @returns All dashboard statistics
 */
export function useDashboardStats() {
  const pmaStats = usePMACertificatesStats();
  const maintenanceStats = useMaintenanceLogsStats();
  const complaintsStats = useComplaintsStats();
  const safetyStats = useSafetyIncidentStats();
  const complianceScore = useComplianceScore();

  const isLoading =
    pmaStats.isLoading ||
    maintenanceStats.isLoading ||
    complaintsStats.isLoading ||
    safetyStats.isLoading ||
    complianceScore.isLoading;

  const isError =
    pmaStats.isError ||
    maintenanceStats.isError ||
    complaintsStats.isError ||
    safetyStats.isError ||
    complianceScore.isError;

  const error =
    pmaStats.error ||
    maintenanceStats.error ||
    complaintsStats.error ||
    safetyStats.error ||
    complianceScore.error;

  return {
    pmaStats: pmaStats.data,
    maintenanceStats: maintenanceStats.data,
    complaintsStats: complaintsStats.data,
    safetyStats: safetyStats.data,
    complianceScore: complianceScore.data,
    isLoading,
    isError,
    error,
  };
}
