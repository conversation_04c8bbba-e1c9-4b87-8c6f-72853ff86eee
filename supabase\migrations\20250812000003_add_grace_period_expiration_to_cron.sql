-- ================================
-- ADD GRACE PERIOD EXPIRATION TO MIDNIGHT CRON JOB
-- ================================
-- Migration: Extend handle_trial_expiration function to also handle
-- expired grace period subscriptions by changing their status to 'suspended'

-- ================================
-- 1. UPDATE CRON FUNCTION TO HANDLE BOTH TRIAL AND GRACE PERIOD EXPIRATION
-- ================================

CREATE OR REPLACE FUNCTION public.handle_trial_and_grace_expiration() 
RETURNS TABLE(
    trial_expired_count INTEGER,
    grace_period_expired_count INTEGER,
    execution_time TIMESTAMPTZ,
    details TEXT
) AS $$
DECLARE
    trial_expiry_count INTEGER := 0;
    grace_expiry_count INTEGER := 0;
    start_time TIMESTAMPTZ := CURRENT_TIMESTAMP;
BEGIN
    -- Handle trial period expiration (convert trial to pending_payment)
    UPDATE public.pma_subscriptions 
    SET 
        status = 'pending_payment',
        trial_ends_at = NULL,
        updated_at = CURRENT_TIMESTAMP
    WHERE status = 'trial' 
    AND trial_ends_at < CURRENT_TIMESTAMP;
    
    -- Get count of expired trials
    GET DIAGNOSTICS trial_expiry_count = ROW_COUNT;
    
    -- Handle grace period expiration (convert grace_period to suspended)
    UPDATE public.pma_subscriptions 
    SET 
        status = 'suspended',
        grace_period_ends = NULL,
        updated_at = CURRENT_TIMESTAMP
    WHERE status = 'grace_period' 
    AND grace_period_ends < CURRENT_TIMESTAMP;
    
    -- Get count of expired grace periods
    GET DIAGNOSTICS grace_expiry_count = ROW_COUNT;
    
    -- Return execution summary
    RETURN QUERY SELECT 
        trial_expiry_count,
        grace_expiry_count,
        start_time,
        format(
            'Expired %s trial subscriptions (converted to pending_payment) and %s grace period subscriptions (converted to suspended).', 
            trial_expiry_count, 
            grace_expiry_count
        );
END;
$$ LANGUAGE plpgsql;

-- Update function comment
COMMENT ON FUNCTION public.handle_trial_and_grace_expiration IS 'Daily function to handle trial and grace period expiration. Converts trial subscriptions to pending_payment status (with trial_ends_at set to NULL) and expired grace period subscriptions to suspended status (with grace_period_ends set to NULL). Called by cron job at 12:00 AM.';

-- ================================
-- 2. UPDATE CRON JOB TO USE NEW FUNCTION
-- ================================

-- Remove old cron job if it exists (ignore error if it doesn't exist)
DO $$
BEGIN
    PERFORM cron.unschedule('handle-trial-expiration');
EXCEPTION
    WHEN OTHERS THEN
        -- Job doesn't exist, continue
        NULL;
END;
$$;

-- Add new cron job for both trial and grace period expiration
SELECT cron.schedule(
    'handle-trial-and-grace-expiration',
    '0 0 * * *', -- Daily at 12:00 AM
    $$SELECT public.handle_trial_and_grace_expiration();$$
);

-- ================================
-- 3. CLEANUP OLD FUNCTION
-- ================================

-- Drop the old function since it's replaced by the new one
DROP FUNCTION IF EXISTS public.handle_trial_expiration();

-- ================================
-- 4. UPDATE DATABASE TYPES (for TypeScript)
-- ================================

-- Note: After running this migration, you should regenerate database types
-- by running: pnpm db:types:local

-- ================================
-- MIGRATION SUMMARY
-- ================================
-- This migration:
-- 1. Replaces handle_trial_expiration with handle_trial_and_grace_expiration
-- 2. Extends the function to also handle grace period expiration
-- 3. Changes expired grace_period subscriptions to suspended status
-- 4. Updates the cron job to use the new function
-- 5. Maintains backward compatibility for trial expiration logic
-- 
-- Result: Every midnight at 12:00 AM, the system will now:
-- - Convert expired trial subscriptions to pending_payment status and set trial_ends_at to NULL
-- - Convert expired grace_period subscriptions to suspended status and set grace_period_ends to NULL
-- - Ensure access_allowed field correctly reflects actual access permissions
-- - Maintain database constraint compliance for status-specific date fields
-- ================================