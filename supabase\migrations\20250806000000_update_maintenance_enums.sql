-- Update maintenance_status and operation_type_enum values
-- This migration updates the enum values to match the new UI requirements

-- Remove default constraints first
ALTER TABLE maintenance_logs ALTER COLUMN status DROP DEFAULT;
ALTER TABLE maintenance_logs ALTER COLUMN operation_log_type DROP DEFAULT;

-- Convert columns to text type to allow updates
ALTER TABLE maintenance_logs ALTER COLUMN status TYPE text;
ALTER TABLE maintenance_logs ALTER COLUMN operation_log_type TYPE text;

-- Update all existing records to use new values
UPDATE maintenance_logs 
SET status = CASE 
    WHEN status = 'fully function' THEN 'in service'
    WHEN status = 'broken' THEN 'out of service'
    ELSE status
END;

-- Update operation_log_type for existing records  
UPDATE maintenance_logs 
SET operation_log_type = CASE 
    WHEN operation_log_type = 'mantrap' THEN 'daily logs'
    ELSE operation_log_type
END;

-- Drop the old enum types (CASCADE to remove dependencies)
DROP TYPE IF EXISTS maintenance_status CASCADE;
DROP TYPE IF EXISTS operation_type_enum CASCADE;

-- Create new enum types with updated values
CREATE TYPE maintenance_status AS ENUM ('in service', 'out of service');
CREATE TYPE operation_type_enum AS ENUM ('daily logs', '14 schedule');

-- Convert columns back to enum types with new values
ALTER TABLE maintenance_logs 
ALTER COLUMN status TYPE maintenance_status USING status::maintenance_status;

ALTER TABLE maintenance_logs 
ALTER COLUMN operation_log_type TYPE operation_type_enum USING operation_log_type::operation_type_enum;

-- Set default values
ALTER TABLE maintenance_logs 
ALTER COLUMN status SET DEFAULT 'in service';

ALTER TABLE maintenance_logs 
ALTER COLUMN operation_log_type SET DEFAULT 'daily logs';

-- Update comments to reflect new enum values
COMMENT ON COLUMN maintenance_logs.status IS 'Status of the equipment after maintenance: in service or out of service';
COMMENT ON COLUMN maintenance_logs.operation_log_type IS 'Type of maintenance operation: daily logs or 14 schedule';
