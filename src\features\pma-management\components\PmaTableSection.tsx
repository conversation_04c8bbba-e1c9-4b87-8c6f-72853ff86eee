'use client';

import { DeleteConfirmationDialog } from '@/components/DeleteConfirmationDialog';
import { TableColumn, TableSection } from '@/components/table-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn, formatInterval } from '@/lib/utils';
import { format, isAfter, isEqual, parseISO } from 'date-fns';
import { Eye, MoreVertical } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import * as React from 'react';
import { useDeletePMACertificate } from '../hooks/use-pma-certificates';
import { PmaDetailModal } from './PmaDetailModal';

import { PMACertificate } from '../types/pma-certificate';

interface TableState {
  sorting?: {
    column: string;
    direction: 'asc' | 'desc';
  };
  columnVisibility: Record<string, boolean>;
}

interface PmaTableSectionProps {
  data: PMACertificate[];
  columns: TableColumn[];
  isLoading: boolean;
  emptyMessage: string;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  pageSize: number;
  onPageSizeChange: (size: number) => void;
  tPma: (key: string) => string;
  tableState: TableState;
  onTableStateChange: (newState: Partial<TableState>) => void;
}

const PmaTableSection = ({
  data,
  columns,
  isLoading,
  emptyMessage,
  currentPage,
  totalPages,
  onPageChange,
  pageSize,
  onPageSizeChange,
  tPma,
  tableState,
  onTableStateChange,
}: PmaTableSectionProps) => {
  const router = useRouter();
  const params = useParams();
  const { mutate: deletePMACertificate, isPending: isDeleting } =
    useDeletePMACertificate();

  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [rowToDelete, setRowToDelete] = React.useState<PMACertificate | null>(
    null,
  );

  // State for detail modal
  const [detailModalOpen, setDetailModalOpen] = React.useState(false);
  const [selectedPma, setSelectedPma] = React.useState<PMACertificate | null>(
    null,
  );

  const handleView = (pma: PMACertificate) => {
    setSelectedPma(pma);
    setDetailModalOpen(true);
  };

  const handleEdit = (pma: PMACertificate) => {
    const locale = params.locale as string;
    router.push(`/${locale}/pmas/edit/${pma.id}`);
  };

  // Custom cell rendering logic
  const defaultRenderCell = (
    row: PMACertificate,
    key: string | number | symbol,
  ) => {
    const columnKey = String(key);
    const value = (row as Record<string, unknown>)[columnKey];

    if (columnKey === 'status') {
      const expiry = row.expiry_date ? parseISO(row.expiry_date) : null;
      const now = new Date();
      let status: 'active' | 'expiring_soon' | 'expired' = 'active';
      if (expiry) {
        if (isAfter(expiry, now)) {
          const oneMonthFromNow = new Date();
          oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
          if (isAfter(expiry, oneMonthFromNow)) {
            status = 'active';
          } else {
            status = 'expiring_soon';
          }
        } else if (isEqual(expiry, now) || isAfter(now, expiry)) {
          status = 'expired';
        }
      }
      let badgeClassName = '';
      switch (status) {
        case 'active':
          badgeClassName =
            'bg-emerald-50 text-emerald-700 ring-1 ring-inset ring-emerald-600/20';
          break;
        case 'expiring_soon':
          badgeClassName =
            'bg-amber-50 text-amber-700 ring-1 ring-inset ring-amber-600/20';
          break;
        case 'expired':
          badgeClassName =
            'bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20';
          break;
      }
      return (
        <Badge className={cn('font-medium capitalize', badgeClassName)}>
          {tPma(`status.${status}`).replace('_', ' ')}
        </Badge>
      );
    }
    if (columnKey === 'inspection_date' && value) {
      try {
        return format(parseISO(String(value)), 'dd MMM yyyy');
      } catch {
        return String(value);
      }
    }
    if (columnKey === 'lift_installation_date' && value) {
      try {
        return format(parseISO(String(value)), 'dd MMM yyyy');
      } catch {
        return String(value);
      }
    }
    if (columnKey === 'total_repair_cost') {
      const numValue = Number(value);
      let badgeColor = 'bg-emerald-100 text-emerald-700 border-emerald-200';
      if (numValue > 10000)
        badgeColor = 'bg-red-100 text-red-700 border-red-200';
      else if (numValue > 5000)
        badgeColor = 'bg-amber-100 text-amber-700 border-amber-200';
      return (
        <span className="flex justify-center">
          <span
            className={`inline-block px-1.5 py-0.5 rounded text-[11px] font-medium border ${badgeColor}`}
          >
            {value ? `RM ${Number(value).toLocaleString()}` : '-'}
          </span>
        </span>
      );
    }
    if (columnKey === 'total_repair_time') {
      return (
        <span className="flex justify-center">
          <span className="inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-[11px] font-medium border bg-blue-50 text-blue-700 border-blue-200">
            <svg
              className="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            {value ? formatInterval(String(value)) : '-'}
          </span>
        </span>
      );
    }
    if (columnKey === 'location') {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="truncate max-w-[180px] block cursor-pointer text-left">
              {String(value)}
            </span>
          </TooltipTrigger>
          <TooltipContent>{String(value)}</TooltipContent>
        </Tooltip>
      );
    }
    if (columnKey === 'actions') {
      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleView(row)}>
                <Eye className="mr-2 h-4 w-4" />
                <span>View</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEdit(row)}>
                <svg
                  className="mr-2 h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
                <span>Edit</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <DeleteConfirmationDialog
            open={deleteDialogOpen && rowToDelete?.id === row.id}
            onOpenChange={(open) => {
              setDeleteDialogOpen(open);
              if (!open) setRowToDelete(null);
            }}
            title="Delete PMA Certificate"
            message="Are you sure you want to delete this PMA certificate? This action cannot be undone."
            confirmText="Delete"
            cancelText="Cancel"
            loading={isDeleting}
            onConfirm={() => {
              deletePMACertificate(row.id);
            }}
            onCancel={() => {
              setRowToDelete(null);
            }}
          />
        </>
      );
    }

    // Default: right-align numbers/dates, left-align text
    if (typeof value === 'number') {
      return <span className="text-right block w-full">{value}</span>;
    }
    if (typeof value === 'string' && /\d{4}-\d{2}-\d{2}/.test(value)) {
      return <span className="text-right block w-full">{value}</span>;
    }
    return (
      <span className="text-left block w-full">{String(value ?? '')}</span>
    );
  };

  return (
    <>
      <TableSection
        data={data}
        columns={columns}
        isLoading={isLoading}
        emptyMessage={emptyMessage}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        pageSize={pageSize}
        onPageSizeChange={onPageSizeChange}
        tableState={tableState}
        onTableStateChange={onTableStateChange}
        renderCell={defaultRenderCell}
      />
      <PmaDetailModal
        pma={selectedPma}
        open={detailModalOpen}
        onOpenChange={setDetailModalOpen}
      />
    </>
  );
};

export default PmaTableSection;
