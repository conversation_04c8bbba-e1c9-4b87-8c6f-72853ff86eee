import { BatchInvoiceView } from '@/features/billing/components/BatchInvoiceView';
import { Metadata } from 'next';

interface PageProps {
  params: Promise<{
    batchId: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { batchId } = await params;
  return {
    title: `Batch Invoice - ${batchId}`,
    description: 'Batch payment invoice for SimPLE billing',
    robots: 'noindex, nofollow', // Don't index invoice pages
  };
}

export default async function BatchInvoicePage({ params }: PageProps) {
  const { batchId } = await params;
  return <BatchInvoiceView batchId={batchId} />;
}
