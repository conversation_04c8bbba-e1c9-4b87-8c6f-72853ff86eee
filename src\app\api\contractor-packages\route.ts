import { ContractorPackageService } from '@/features/billing/services/contractor-package.service';
import { authenticateWithPermission } from '@/features/auth';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Schema for creating/updating contractor packages
const CreatePackageSchema = z.object({
  name: z.string().min(1, 'Package name is required'),
  pricing: z.number().positive('Pricing must be positive'),
  description: z.string().optional(),
  is_active: z.boolean().optional().default(true),
});

/**
 * GET /api/contractor-packages
 * List all contractor packages (admins only) or active packages
 */
export async function GET(request: NextRequest) {
  try {
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('include_inactive') === 'true';
    const withUsage = searchParams.get('with_usage') === 'true';

    // Only admins can see inactive packages and usage data
    const isAdmin = user.user_role === 'admin';
    const canSeeInactive = isAdmin && includeInactive;
    const canSeeUsage = isAdmin && withUsage;

    let result;
    if (canSeeUsage) {
      result = await ContractorPackageService.getAllWithUsageCount();
    } else {
      result = await ContractorPackageService.getAll(canSeeInactive);
    }

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ packages: result.data });
  } catch (error) {
    console.error('Get contractor packages error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * POST /api/contractor-packages
 * Create a new contractor package (admins only)
 */
export async function POST(request: NextRequest) {
  try {
    const { user, error } = await authenticateWithPermission('projects.create');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Only admins can create packages
    if (user.user_role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validation = CreatePackageSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const packageData = validation.data;
    const result = await ContractorPackageService.create(packageData);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json(
      {
        message: 'Package created successfully',
        package: result.data,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error('Create contractor package error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
