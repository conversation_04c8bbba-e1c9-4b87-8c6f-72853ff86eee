import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ProfileService,
  type UpdateProfileData,
} from '../services/profile.service';

export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      userId,
      data,
    }: {
      userId: string;
      data: UpdateProfileData;
    }) => {
      return ProfileService.updateProfile(userId, data);
    },
    onSuccess: (updatedProfile) => {
      // Update the user-with-profile query cache
      queryClient.setQueryData(['user-with-profile'], (oldData: unknown) => {
        if (oldData && typeof oldData === 'object' && 'profile' in oldData) {
          return {
            ...oldData,
            profile: updatedProfile,
          };
        }
        return oldData;
      });

      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (error) => {
      console.error('Profile update error:', error);
    },
  });
}
