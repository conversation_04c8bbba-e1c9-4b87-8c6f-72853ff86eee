import { z } from 'zod';

// Create a function that takes translation messages
export const createPmaFormSchema = (messages: {
  locationRequired: string;
  pmaNumberRequired: string;
  inspectionDateRequired: string;
  liftInspectionDateRequired: string;
  competentPersonRequired: string;
  pmaExpiryRequired: string;
}) =>
  z.object({
    location: z.string().min(1, messages.locationRequired),
    pmaNumber: z.string().min(1, messages.pmaNumberRequired),
    inspectionDate: z.string().min(1, messages.inspectionDateRequired),
    liftInspectionDate: z.string().min(1, messages.liftInspectionDateRequired),
    competentPersonId: z.string().min(1, messages.competentPersonRequired),
    pmaExpiryDate: z.string().min(1, messages.pmaExpiryRequired),
    certificateFile: z.instanceof(File).optional(),
  });

// Default schema for backward compatibility with all new fields
export const pmaFormSchema = z.object({
  location: z
    .string()
    .min(1, 'LIF location is required')
    .max(500, 'Location must be less than 500 characters'),
  pmaNumber: z
    .string()
    .min(1, 'PMA number is required')
    .max(50, 'PMA number must be less than 50 characters')
    .transform((val) => val.trim())
    .refine((val) => val.length > 0, {
      message: 'PMA number cannot be empty',
    }),
  inspection_date: z.string().min(1, 'Inspection date is required'),
  liftInstallationDate: z.string().min(1, 'Lift installation date is required'),
  competentPersonId: z
    .string()
    .min(1, 'Competent person is required')
    .uuid('Invalid competent person ID'),
  pmaExpiryDate: z.string().min(1, 'PMA expiry date is required'),

  // Removed the future date validation to allow backdates, current dates, and future dates
  certificateFile: z
    .instanceof(File)
    .optional()
    .refine((file) => {
      if (!file) return true;
      return file.size <= 10 * 1024 * 1024; // 10MB
    }, 'File size must be less than 10MB')
    .refine((file) => {
      if (!file) return true;
      return ['application/pdf', 'image/jpeg', 'image/png'].includes(file.type);
    }, 'Only PDF, JPG, and PNG files are allowed'),
});

// Edit mode schema - only expiry date is required, others are optional
export const pmaEditFormSchema = z.object({
  location: z
    .string()
    .max(500, 'Location must be less than 500 characters')
    .optional(),
  pmaNumber: z
    .string()
    .max(50, 'PMA number must be less than 50 characters')
    .optional(),
  inspectionDate: z.string().optional(),
  liftInstallationDate: z.string().optional(),
  competentPersonId: z.string().optional(),
  pmaExpiryDate: z.string().min(1, 'PMA expiry date is required'),
  certificateFile: z
    .instanceof(File)
    .optional()
    .refine((file) => {
      if (!file) return true;
      return file.size <= 10 * 1024 * 1024; // 10MB
    }, 'File size must be less than 10MB')
    .refine((file) => {
      if (!file) return true;
      return ['application/pdf', 'image/jpeg', 'image/png'].includes(file.type);
    }, 'Only PDF, JPG, and PNG files are allowed'),
});

export type PmaFormSchema = z.infer<typeof pmaFormSchema>;
export type PmaEditFormSchema = z.infer<typeof pmaEditFormSchema>;
