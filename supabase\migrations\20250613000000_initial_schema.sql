-- ================================
-- CONSOLIDATED SCHEMA MIGRATION (Merged Project Roles + Invitations)
-- Complete migration with audit constraints, indexes, and enforcement triggers
-- ================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ================================
-- ENUM TYPES
-- ================================
DO $$ BEGIN CREATE TYPE user_role AS ENUM ('contractor', 'admin', 'viewer'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN CREATE TYPE admin_access_mode AS ENUM ('state', 'project'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN CREATE TYPE company_type AS ENUM ('COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN CREATE TYPE complaint_status AS ENUM ('open','on_hold','closed'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN CREATE TYPE pma_status AS ENUM ('valid','validating','invalid'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN CREATE TYPE state_code AS ENUM ('JH','KD','KT','ML','NS','PH','PN','PK','PL','SB','SW','SL','TR','WP','LBN','PW','OTH'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN CREATE TYPE project_role AS ENUM ('technician','competent_person','admin','viewer'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;

-- ================================
-- CONTRACTORS
-- ================================
CREATE TABLE contractors (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  contractor_type company_type NOT NULL,
  oem_name text,
  hotline text,
  is_active boolean NOT NULL DEFAULT true,
  code text UNIQUE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid
);

-- ================================
-- AGENCIES
-- ================================
CREATE TABLE agencies (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL UNIQUE,
  state state_code,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid
);

-- ================================
-- USERS
-- ================================
CREATE TABLE users (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  phone_number text,
  email text NOT NULL,
  user_role user_role NOT NULL,
  admin_access_mode admin_access_mode,
  monitoring_state state_code,
  contractor_id uuid REFERENCES contractors(id),
  onboarding_completed boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid,
  CONSTRAINT chk_admin_mode_validity CHECK (
    (user_role = 'admin' AND admin_access_mode IS NOT NULL) OR
    (user_role != 'admin' AND admin_access_mode IS NULL)
  ),
  CONSTRAINT chk_state_monitoring CHECK (
    (admin_access_mode = 'state' AND monitoring_state IS NOT NULL) OR
    (admin_access_mode != 'state' AND monitoring_state IS NULL)
  )
);

-- ================================
-- PROJECTS
-- ================================
CREATE TABLE projects (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  contractor_id uuid REFERENCES contractors(id) ON DELETE SET NULL,
  agency_id uuid REFERENCES agencies(id) ON DELETE SET NULL,
  name text NOT NULL,
  code text UNIQUE,
  state state_code,
  location text,
  start_date date,
  end_date date,
  status text DEFAULT 'active',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid
);

-- ================================
-- PROJECT_USERS (Unified Roles)
-- ================================
CREATE TABLE project_users (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role project_role NOT NULL,
  assigned_date date DEFAULT CURRENT_DATE,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid,
  UNIQUE(project_id, user_id, role)
);

-- ================================
-- PROJECT_INVITATIONS
-- ================================
CREATE TABLE project_invitations (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  inviter_user_id uuid REFERENCES users(id) ON DELETE SET NULL,
  invitee_user_id uuid REFERENCES users(id) ON DELETE SET NULL,
  invitee_email text NOT NULL,
  role project_role NOT NULL,
  token text UNIQUE NOT NULL,
  status text NOT NULL DEFAULT 'pending',
  expiry_date timestamptz NOT NULL,
  created_at timestamptz DEFAULT now(),
  responded_at timestamptz,
  responded_by uuid REFERENCES users(id) ON DELETE SET NULL
);

-- ================================
-- PMA_CERTIFICATES
-- ================================
CREATE TABLE pma_certificates (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES users(id) ON DELETE SET NULL,
  competent_person_id uuid REFERENCES users(id) ON DELETE SET NULL,
  expiry_date date NOT NULL,
  status pma_status NOT NULL DEFAULT 'validating',
  file_url text,
  pma_number text,
  state state_code,
  project_id uuid REFERENCES projects(id) ON DELETE SET NULL,
  location text,
  total_repair_cost decimal,
  total_repair_time interval,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid
);

-- ================================
-- MAINTENANCE_LOGS
-- ================================
CREATE TABLE maintenance_logs (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  contractor_id uuid REFERENCES contractors(id) ON DELETE SET NULL,
  pma_id uuid REFERENCES pma_certificates(id) ON DELETE SET NULL,
  operation_log_type text NOT NULL,
  log_date date NOT NULL,
  person_in_charge_name text,
  person_in_charge_phone text,
  description text,
  project_id uuid REFERENCES projects(id) ON DELETE SET NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid
);

-- ================================
-- COMPLAINTS
-- ================================
CREATE TABLE complaints (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  email text NOT NULL,
  number text UNIQUE NOT NULL,
  date date NOT NULL,
  contractor_name text,
  location text,
  no_pma_lif text,
  description text,
  expected_completion_date date,
  involves_mantrap boolean,
  actual_completion_date date,
  repair_completion_time time,
  cause_of_damage text,
  correction_action text,
  proof_of_repair_urls text[],
  repair_cost decimal,
  status complaint_status NOT NULL DEFAULT 'open',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid,
  contractor_id uuid REFERENCES contractors(id) ON DELETE SET NULL,
  project_id uuid REFERENCES projects(id) ON DELETE SET NULL,
  pma_id uuid REFERENCES pma_certificates(id) ON DELETE SET NULL
);

-- ================================
-- TRIGGERS TO ENFORCE PROJECT_ADMIN MODE
-- ================================
CREATE FUNCTION enforce_project_admin_mode() RETURNS trigger AS $$
BEGIN
  IF NEW.role = 'admin' THEN
    PERFORM 1 FROM users WHERE id = NEW.user_id AND admin_access_mode = 'project';
    IF NOT FOUND THEN
      RAISE EXCEPTION 'User % is not a project-level admin', NEW.user_id;
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_validate_project_admin
  BEFORE INSERT OR UPDATE ON project_users
  FOR EACH ROW EXECUTE FUNCTION enforce_project_admin_mode();

-- ================================
-- ADD FOREIGN KEY CONSTRAINTS (Audit Trail)
-- ================================
ALTER TABLE contractors ADD CONSTRAINT fk_contractors_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_contractors_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_contractors_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);
ALTER TABLE agencies ADD CONSTRAINT fk_agencies_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_agencies_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_agencies_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);
ALTER TABLE users ADD CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_users_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_users_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);
ALTER TABLE projects ADD CONSTRAINT fk_projects_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_projects_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_projects_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);
ALTER TABLE project_users ADD CONSTRAINT fk_project_users_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_project_users_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_project_users_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);
ALTER TABLE project_invitations ADD CONSTRAINT fk_proj_inv_inviter FOREIGN KEY (inviter_user_id) REFERENCES users(id), ADD CONSTRAINT fk_proj_inv_responder FOREIGN KEY (responded_by) REFERENCES users(id);
ALTER TABLE pma_certificates ADD CONSTRAINT fk_pma_cert_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_pma_cert_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_pma_cert_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);
ALTER TABLE maintenance_logs ADD CONSTRAINT fk_maint_logs_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_maint_logs_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_maint_logs_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);
ALTER TABLE complaints ADD CONSTRAINT fk_complaints_created_by FOREIGN KEY (created_by) REFERENCES users(id), ADD CONSTRAINT fk_complaints_updated_by FOREIGN KEY (updated_by) REFERENCES users(id), ADD CONSTRAINT fk_complaints_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================
CREATE INDEX idx_agencies_state ON agencies(state);
CREATE INDEX idx_contractors_deleted_at ON contractors(deleted_at);
CREATE INDEX idx_agencies_deleted_at ON agencies(deleted_at);

CREATE INDEX idx_users_contractor_id ON users(contractor_id);
CREATE INDEX idx_users_monitoring_state ON users(monitoring_state);
CREATE INDEX idx_users_user_role ON users(user_role);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

CREATE INDEX idx_projects_contractor_id ON projects(contractor_id);
CREATE INDEX idx_projects_agency_id ON projects(agency_id);
CREATE INDEX idx_projects_deleted_at ON projects(deleted_at);

CREATE INDEX idx_project_users_project_id ON project_users(project_id);
CREATE INDEX idx_project_users_user_id ON project_users(user_id);
CREATE INDEX idx_project_users_role ON project_users(role);
CREATE INDEX idx_project_users_is_active ON project_users(is_active);
CREATE INDEX idx_project_users_deleted_at ON project_users(deleted_at);

CREATE INDEX idx_project_inv_project_id ON project_invitations(project_id);
CREATE INDEX idx_project_inv_invitee_user ON project_invitations(invitee_user_id);
CREATE INDEX idx_project_inv_email ON project_invitations(invitee_email);
CREATE INDEX idx_project_inv_status ON project_invitations(status);

CREATE INDEX idx_pma_cert_project_id ON pma_certificates(project_id);
CREATE INDEX idx_pma_cert_deleted_at ON pma_certificates(deleted_at);
CREATE INDEX idx_maintenance_logs_pma_id ON maintenance_logs(pma_id);
CREATE INDEX idx_maintenance_logs_contractor_id ON maintenance_logs(contractor_id);
CREATE INDEX idx_maintenance_logs_deleted_at ON maintenance_logs(deleted_at);

CREATE INDEX idx_complaints_pma_id ON complaints(pma_id);
CREATE INDEX idx_complaints_contractor_id ON complaints(contractor_id);
CREATE INDEX idx_complaints_project_id ON complaints(project_id);
CREATE INDEX idx_complaints_deleted_at ON complaints(deleted_at);

-- ================================
-- END OF MIGRATION
-- ================================
