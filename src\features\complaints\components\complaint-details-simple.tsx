'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { PhotoViewer } from '@/components/ui/photo-viewer';
import {
  Building,
  Calendar,
  Edit,
  FileText,
  Mail,
  MapPin,
  User,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ComplaintUI } from '../types/ui-types';

interface ComplaintDetailsProps {
  complaint: ComplaintUI;
  onClose: () => void;
  onEdit?: () => void;
}

export function ComplaintDetails({
  complaint,
  onClose,
  onEdit,
}: ComplaintDetailsProps) {
  const t = useTranslations('complaints');

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'in_progress':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'resolved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending_approval':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'verified':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'closed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open':
        return t('status.open');
      case 'in_progress':
        return t('status.inProgress');
      case 'resolved':
        return t('status.resolved');
      case 'pending_approval':
        return t('status.pendingApproval');
      case 'verified':
        return t('status.verified');
      case 'closed':
        return t('status.closed');
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {t('form.title')} #{complaint.number}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            {t('table.dateSubmitted')}:{' '}
            {formatDate(complaint.created_at || new Date().toISOString())}
          </p>
        </div>{' '}
        <div className="flex items-center gap-3">
          <Badge className={getStatusColor(complaint.status)}>
            {getStatusText(complaint.status)}
          </Badge>
          {onEdit && complaint.follow_up !== 'verified' && (
            <Button variant="outline" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              {t('table.actions')}
            </Button>
          )}
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>

      <Separator />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t('form.sectionA.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Email</p>
                <p className="text-sm text-gray-600">{complaint.email}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Damage Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Damage Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Damage Date</p>
                <p className="text-sm text-gray-600">
                  {formatDate(complaint.date)}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Expected Completion</p>
                <p className="text-sm text-gray-600">
                  {formatDate(complaint.expected_completion_date)}
                </p>
              </div>
            </div>
            {complaint.actual_completion_date && (
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Actual Completion</p>
                  <p className="text-sm text-gray-600">
                    {formatDate(complaint.actual_completion_date)}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Location Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium">Location</p>
              <p className="text-sm text-gray-600">{complaint.location}</p>
            </div>
            {complaint.involves_mantrap && (
              <div>
                <p className="text-sm font-medium">Mantrap Involved</p>
                <p className="text-sm text-gray-600">Yes</p>
              </div>
            )}
            {complaint.no_pma_lif && (
              <div>
                <p className="text-sm font-medium">NO PMA LIF</p>
                <p className="text-sm text-gray-600">{complaint.no_pma_lif}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contractor Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Contractor Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium">Contractor/Company Name</p>
              <p className="text-sm text-gray-600">
                {complaint.contractor_name || 'N/A'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      {complaint.description && (
        <Card>
          <CardHeader>
            <CardTitle>Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 whitespace-pre-wrap">
              {complaint.description}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Before Repair Photos */}
      {complaint.before_repair_files &&
        complaint.before_repair_files.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Before Repair Photos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PhotoViewer
                photos={complaint.before_repair_files.map((url, index) => ({
                  url,
                  name: `Before Repair Photo ${index + 1}`,
                }))}
                title="Before Repair Photos"
              />
            </CardContent>
          </Card>
        )}

      {/* Proof of Repair Files */}
      {complaint.proof_of_repair_urls &&
        complaint.proof_of_repair_urls.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Proof of Repair Files
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PhotoViewer
                photos={complaint.proof_of_repair_urls.map((url, index) => ({
                  url,
                  name: `Repair Evidence ${index + 1}`,
                }))}
                title="Proof of Repair Files"
              />
            </CardContent>
          </Card>
        )}
    </div>
  );
}
