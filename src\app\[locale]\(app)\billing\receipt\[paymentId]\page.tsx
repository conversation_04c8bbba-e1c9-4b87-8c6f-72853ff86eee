import { PaymentReceiptView } from '@/features/billing/components/PaymentReceiptView';
import { Metadata } from 'next';

interface PageProps {
  params: Promise<{
    paymentId: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { paymentId } = await params;
  return {
    title: `Payment Receipt - ${paymentId}`,
    description: 'Payment receipt for SimPLE billing',
    robots: 'noindex, nofollow', // Don't index receipt pages
  };
}

export default async function PaymentReceiptPage({ params }: PageProps) {
  const { paymentId } = await params;
  return <PaymentReceiptView paymentId={paymentId} />;
}
