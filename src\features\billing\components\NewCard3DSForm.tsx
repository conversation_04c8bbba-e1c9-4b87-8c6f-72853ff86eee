'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { CreditCard, ExternalLink, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSavedCards } from '../hooks/useSavedCards';

export interface NewCard3DSFormProps {
  contractorId: string;
  onSuccess?: (result: { card_id: string; token: string }) => void;
  className?: string;
  disabled?: boolean;
}

export function NewCard3DSForm({
  contractorId,
  onSuccess,
  className,
  disabled = false,
}: NewCard3DSFormProps) {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [saveCard, setSaveCard] = useState(true);
  const [pendingCardId, setPendingCardId] = useState<string | null>(null);

  const {
    createAndSaveCard,
    getCardResult,
    refetch,
    isCreatingAndSaving,
    isGettingCardResult,
    createAndSaveError,
    getCardResultError,
  } = useSavedCards(contractorId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim() || !name.trim() || !phone.trim()) {
      return;
    }

    try {
      // Step 1: Create card and get redirect URL
      const result = await createAndSaveCard({
        email: email.trim(),
        name: name.trim(),
        phone: phone.trim(),
        contractor_id: contractorId,
        save_card: saveCard,
      });

      // Store pending card ID for result retrieval
      setPendingCardId(result.card_id);

      // Step 2: Redirect user to 3DS authentication
      window.open(
        result.redirect_url,
        '_blank',
        'width=600,height=700,scrollbars=yes,resizable=yes',
      );

      // Step 3: Start polling for result (in a real app, use WebSocket or Server-Sent Events)
      pollForCardResult(result.card_id);
    } catch (error) {
      console.error('Failed to create card:', error);
    }
  };

  const pollForCardResult = async (cardId: string) => {
    const maxAttempts = 30; // 5 minutes with 10-second intervals
    let attempts = 0;

    const poll = async () => {
      try {
        const result = await getCardResult(cardId);

        if (result) {
          setPendingCardId(null);

          // Refresh saved cards list to show the newly saved card
          refetch();

          onSuccess?.({
            card_id: result.card_id,
            token: result.token,
          });
          return;
        }
      } catch {
        // Card result not ready yet, continue polling
      }

      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(poll, 10000); // Poll every 10 seconds
      } else {
        setPendingCardId(null);
        console.error('Timeout waiting for card result');
      }
    };

    // Start polling after a short delay
    setTimeout(poll, 3000);
  };

  const isLoading = isCreatingAndSaving || isGettingCardResult;
  const error = createAndSaveError || getCardResultError;

  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <Label className="text-base font-medium">
          Add New Card (3DS Secure)
        </Label>
        <p className="text-sm text-muted-foreground mt-1">
          Your card details will be entered securely on BillPlz&apos;s payment
          page
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            required
            disabled={disabled || isLoading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="name">Cardholder Name</Label>
          <Input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="John Doe"
            required
            disabled={disabled || isLoading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            type="tel"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            placeholder="+60123456789"
            required
            disabled={disabled || isLoading}
          />
          <p className="text-xs text-muted-foreground">
            Malaysian format: +60XXXXXXXXX
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="save-card"
            checked={saveCard}
            onCheckedChange={(checked) => setSaveCard(checked === true)}
            disabled={disabled || isLoading}
          />
          <Label
            htmlFor="save-card"
            className="text-sm font-normal cursor-pointer"
          >
            Save this card for future payments
          </Label>
        </div>

        <Button
          type="submit"
          disabled={
            disabled ||
            isLoading ||
            !email.trim() ||
            !name.trim() ||
            !phone.trim()
          }
          className="w-full"
        >
          {isCreatingAndSaving && (
            <>
              <CreditCard className="mr-2 h-4 w-4 animate-pulse" />
              Creating secure payment session...
            </>
          )}
          {isGettingCardResult && (
            <>
              <CreditCard className="mr-2 h-4 w-4 animate-pulse" />
              Waiting for card authentication...
            </>
          )}
          {!isLoading && (
            <>
              <ExternalLink className="mr-2 h-4 w-4" />
              Add Card via 3DS
            </>
          )}
        </Button>
      </form>

      {isCreatingAndSaving && (
        <Alert>
          <CreditCard className="h-4 w-4 animate-pulse" />
          <AlertDescription>
            Creating secure payment session...
          </AlertDescription>
        </Alert>
      )}

      {pendingCardId && !isGettingCardResult && (
        <Alert>
          <CreditCard className="h-4 w-4" />
          <AlertDescription>
            Please complete the card authentication in the popup window.
            We&apos;ll automatically detect when you&apos;re done.
          </AlertDescription>
        </Alert>
      )}

      {isGettingCardResult && (
        <Alert>
          <CreditCard className="h-4 w-4 animate-pulse" />
          <AlertDescription>
            Processing your card details and saving securely...
          </AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error.message || 'Failed to process card. Please try again.'}
          </AlertDescription>
        </Alert>
      )}

      <div className="p-3 bg-muted/30 rounded-lg">
        <p className="text-xs text-muted-foreground">
          <strong>Secure Process:</strong> You&apos;ll be redirected to
          BillPlz&apos;s secure payment page where you can safely enter your
          card details. Your information is protected by 3D Secure
          authentication.
        </p>
      </div>
    </div>
  );
}
