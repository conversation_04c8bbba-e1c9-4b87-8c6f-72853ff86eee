-- ================================
-- FIX PRO-RATED AMOUNT CALCULATION WITH BASE AMOUNT
-- Update calculate_prorated_pma_amount to accept base_amount parameter
-- to work correctly with contractor package pricing system
-- ================================

-- ================================
-- 1. UPDATE PRO-RATING FUNCTION TO ACCEPT BASE AMOUNT
-- ================================

-- Drop the old function
DROP FUNCTION IF EXISTS public.calculate_prorated_pma_amount(TIMESTAMPTZ, DATE);

-- Create new function that accepts base_amount parameter
CREATE OR REPLACE FUNCTION public.calculate_prorated_pma_amount(
    subscription_created_date TIMESTAMPTZ,
    calculation_date DATE DEFAULT CURRENT_DATE,
    base_amount NUMERIC(10,2) DEFAULT 150.00
) RETURNS NUMERIC(10,2) AS $$
DECLARE
    current_day INTEGER;
    days_in_month INTEGER;
    days_until_27th INTEGER;
    prorated_amount NUMERIC(10,2);
BEGIN
    -- Get current day of the calculation date
    current_day := EXTRACT(DAY FROM calculation_date);
    
    -- Get total days in the current month
    days_in_month := EXTRACT(DAY FROM (
        DATE_TRUNC('MONTH', calculation_date) + INTERVAL '1 MONTH - 1 DAY'
    ));
    
    -- If subscription was created after 27th of current month or today is after 27th
    -- Return full amount for next month's billing cycle
    IF current_day > 27 OR EXTRACT(DAY FROM subscription_created_date) > 27 THEN
        RETURN base_amount;
    END IF;
    
    -- Calculate days remaining until 27th (inclusive)
    days_until_27th := 27 - current_day + 1;
    
    -- If no days left until 27th, return full amount for next cycle
    IF days_until_27th <= 0 THEN
        RETURN base_amount;
    END IF;
    
    -- Calculate pro-rated amount: (days_until_27th / days_in_month) * base_amount
    prorated_amount := ROUND(
        (days_until_27th::NUMERIC / days_in_month::NUMERIC) * base_amount, 
        2
    );
    
    -- Ensure minimum amount of RM1.00
    IF prorated_amount < 1.00 THEN
        prorated_amount := 1.00;
    END IF;
    
    RETURN prorated_amount;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Update function comment
COMMENT ON FUNCTION public.calculate_prorated_pma_amount IS 'Calculates pro-rated PMA subscription amount based on days remaining until 27th of month. Now accepts base_amount parameter to work with contractor package pricing.';

-- ================================
-- 2. NOTE ON SUBSCRIPTION CREATION
-- ================================

-- No need to update create_pma_subscription_with_trial function since:
-- 1. The package integration triggers automatically set the amount field
-- 2. The amount field will always contain the current package pricing
-- 3. Pro-rating will now work correctly using the amount from the subscription record

-- ================================
-- MIGRATION COMPLETE
-- Migration fixes:
-- 1. Updated calculate_prorated_pma_amount to accept base_amount parameter
-- 2. Updated subscription creation function to use actual package pricing
-- 3. Maintained backward compatibility with default base_amount of 150.00
-- 4. Pro-rating now works correctly with contractor package system
-- ================================