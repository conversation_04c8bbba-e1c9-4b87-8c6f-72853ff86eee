import { BulkPaymentService } from '@/features/billing/services/bulk-payment.service';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const newCardDataSchema = z.object({
  number: z.string().min(13).max(19),
  exp_month: z.number().int().min(1).max(12),
  exp_year: z.number().int().min(new Date().getFullYear()),
  cvc: z.string().regex(/^\d{3,4}$/),
  name: z.string().min(1).max(100),
  save_card: z.boolean().optional().default(false),
});

const bulkPaymentRequestSchema = z
  .object({
    contractor_id: z.string().uuid(),
    user_id: z.string().uuid(),
    subscription_ids: z.array(z.string().uuid()).min(1),
    total_amount: z.number().positive(),
    payment_method: z.enum(['billplz_redirect', 'saved_card', 'new_card']),
    card_token: z.string().optional(),
    new_card_data: newCardDataSchema.optional(),
  })
  .refine(
    (data) => {
      // If payment method is saved_card, card_token is required
      if (data.payment_method === 'saved_card' && !data.card_token) {
        return false;
      }
      // If payment method is new_card, new_card_data is required
      if (data.payment_method === 'new_card' && !data.new_card_data) {
        return false;
      }
      return true;
    },
    {
      message:
        "card_token is required for 'saved_card' and new_card_data is required for 'new_card'",
      path: ['card_token', 'new_card_data'],
    },
  );

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = bulkPaymentRequestSchema.parse(body);

    const result =
      await BulkPaymentService.createConsolidatedPayment(validatedData);

    if (!result.success) {
      return NextResponse.json(
        {
          error: result.error,
          details: result.details,
        },
        { status: 400 },
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        payment_url: result.data!.payment_url,
        billplz_bill_id: result.data!.billplz_bill_id,
        charge_id: result.data!.charge_id,
        payment_record_ids: result.data!.payment_record_ids,
        total_amount: result.data!.total_amount,
        processed_subscriptions: result.data!.processed_subscriptions,
        payment_method: result.data!.payment_method,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('Bulk payment API error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}
