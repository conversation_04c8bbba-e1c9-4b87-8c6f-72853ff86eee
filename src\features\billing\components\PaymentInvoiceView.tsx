'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Download, FileText, Printer } from 'lucide-react';
import Image from 'next/image';
import { usePaymentInvoice } from '../hooks/usePaymentInvoice';

interface PaymentInvoiceViewProps {
  paymentId: string;
}

export function PaymentInvoiceView({ paymentId }: PaymentInvoiceViewProps) {
  const {
    data: paymentRecord,
    isLoading,
    error,
  } = usePaymentInvoice({
    paymentId,
    enabled: !!paymentId,
  });

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    window.print();
  };

  if (isLoading) {
    return <PaymentInvoiceSkeleton />;
  }

  if (error || !paymentRecord) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">
              Invoice Not Available
            </h2>
            <p className="text-muted-foreground">
              {error instanceof Error
                ? error.message
                : 'Unable to load invoice. Please try again later.'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate due date (30 days from created_at)
  const dueDate = new Date(paymentRecord.created_at);
  dueDate.setDate(dueDate.getDate() + 30);

  return (
    <div className="min-h-screen bg-gray-50 p-4 print:bg-white print:p-0">
      <div className="max-w-2xl mx-auto">
        {/* Print/Download Actions - Hidden in print */}
        <div className="mb-6 print:hidden">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Payment Invoice</h1>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </div>
        </div>

        {/* Invoice Content */}
        <Card className="print:shadow-none print:border-none">
          <CardHeader className="text-center border-b print:border-b-2">
            <div className="space-y-4">
              <div className="flex justify-center">
                <Image
                  src="/Simple-Logo-Gradient.svg"
                  alt="SimPLE Logo"
                  width={120}
                  height={30}
                  className="print:w-[120px] print:h-[30px]"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Simple Project Lifecycle Enhancement
                </p>
                <p className="text-xs text-muted-foreground">INVOICE</p>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            {/* Invoice Header */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Invoice Details</h3>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="text-muted-foreground">Invoice ID:</span>
                    <br />
                    <span className="font-mono">{paymentRecord.id}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Invoice Date:</span>
                    <br />
                    <span>{formatDate(paymentRecord.created_at)}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Project Information</h3>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="text-muted-foreground">PMA Number:</span>
                    <br />
                    <span className="font-mono">
                      {
                        paymentRecord.pma_subscription.pma_certificates
                          .pma_number
                      }
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Project:</span>
                    <br />
                    <span>
                      {
                        paymentRecord.pma_subscription.pma_certificates.projects
                          .name
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Invoice Items */}
            <div>
              <h3 className="font-semibold mb-4">Invoice Items</h3>
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50 print:bg-gray-100">
                    <tr>
                      <th className="text-left p-3 font-medium">Description</th>
                      <th className="text-right p-3 font-medium">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t">
                      <td className="p-3">
                        <div>
                          <div className="font-medium">
                            PMA Subscription Fee
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Monthly subscription for PMA{' '}
                            {
                              paymentRecord.pma_subscription.pma_certificates
                                .pma_number
                            }
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            Billing Period:{' '}
                            {formatMonthYear(paymentRecord.created_at)}
                          </div>
                        </div>
                      </td>
                      <td className="p-3 text-right font-medium">
                        {formatCurrency(paymentRecord.amount)}
                      </td>
                    </tr>
                  </tbody>
                  <tfoot className="border-t bg-gray-50 print:bg-gray-100">
                    <tr>
                      <td className="p-3 font-semibold">Total Amount Due</td>
                      <td className="p-3 text-right font-bold text-lg">
                        {formatCurrency(paymentRecord.amount)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Footer */}
            <div className="text-center text-xs text-muted-foreground pt-4 border-t">
              <p>
                This is a computer-generated invoice. No signature is required.
              </p>
              <p className="mt-1">
                Generated on {formatDate(new Date().toISOString())}
              </p>
              {paymentRecord.status !== 'paid' && (
                <p className="mt-2 text-red-600 font-medium">
                  Payment is due within 30 days of invoice date.
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function PaymentInvoiceSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
        </div>

        <Card>
          <CardHeader className="text-center border-b">
            <Skeleton className="h-6 w-24 mx-auto mb-2" />
            <Skeleton className="h-4 w-48 mx-auto mb-1" />
            <Skeleton className="h-3 w-32 mx-auto" />
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <Skeleton className="h-5 w-32" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-5 w-32" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Skeleton className="h-5 w-32" />
              <div className="border rounded-lg p-4 space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-6 w-24" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function _getStatusBadge(status: string) {
  const statusConfig = {
    paid: {
      variant: 'default' as const,
      color: 'bg-green-100 text-green-800',
      text: 'Paid',
    },
    pending: {
      variant: 'secondary' as const,
      color: 'bg-yellow-100 text-yellow-800',
      text: 'Pending',
    },
    failed: {
      variant: 'destructive' as const,
      color: 'bg-red-100 text-red-800',
      text: 'Failed',
    },
    cancelled: {
      variant: 'outline' as const,
      color: 'bg-gray-100 text-gray-800',
      text: 'Cancelled',
    },
    refunded: {
      variant: 'outline' as const,
      color: 'bg-blue-100 text-blue-800',
      text: 'Refunded',
    },
  } as const;

  const config =
    statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

  return (
    <Badge variant={config.variant} className={config.color}>
      {config.text}
    </Badge>
  );
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-MY', {
    style: 'currency',
    currency: 'MYR',
  }).format(amount / 100);
}

function formatDate(dateString: string): string {
  return new Intl.DateTimeFormat('en-MY', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString));
}

function formatMonthYear(dateString: string): string {
  return new Intl.DateTimeFormat('en-MY', {
    year: 'numeric',
    month: 'long',
  }).format(new Date(dateString));
}
