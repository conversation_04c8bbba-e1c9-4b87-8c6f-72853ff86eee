'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  useCompetentPersonsSearch,
  useCompetentPersonsStats,
} from '@/features/competent-persons';
import { usePermissions } from '@/hooks/use-permissions';
import { createClient } from '@/lib/supabase';
import type { CompetentPerson } from '@/types/competent-person';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  Award,
  Building2,
  Calendar,
  Edit,
  FileText,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  UserCheck,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';
import { AddCPModal } from './components/add-cp-modal';
import { CertificatePreviewModal } from './components/certificate-preview-modal';

interface ProjectWithCPs {
  id: string;
  name: string;
  code: string;
  location: string;
  contractor_name: string;
  competent_persons: Array<{
    id: string;
    name: string;
    ic_no: string;
    phone_no?: string;
    cp_type: string;
    cp_registeration_no?: string;
    cert_exp_date?: string;
    pma_count: number;
  }>;
}

async function fetchProjectsWithCPs(): Promise<ProjectWithCPs[]> {
  const supabase = createClient();
  
  const { data: projects, error } = await supabase
    .from('projects')
    .select(`
      id,
      name,
      code,
      location,
      contractors!projects_contractor_id_fkey(name),
      pma_certificates(
        competent_person_id,
        competent_person!pma_certificates_competent_person_id_fkey(
          id,
          name,
          ic_no,
          phone_no,
          cp_type,
          cp_registeration_no,
          cert_exp_date
        )
      )
    `)
    .is('deleted_at', null);

  if (error) throw error;

  return projects.map(project => {
    const cpMap = new Map();
    
    project.pma_certificates?.forEach(pma => {
      if (pma.competent_person) {
        const cp = pma.competent_person;
        if (!cpMap.has(cp.id)) {
          cpMap.set(cp.id, {
            ...cp,
            pma_count: 0
          });
        }
        cpMap.get(cp.id).pma_count++;
      }
    });

    return {
      id: project.id,
      name: project.name,
      code: project.code || '',
      location: project.location || '',
      contractor_name: project.contractors?.name || 'Unknown',
      competent_persons: Array.from(cpMap.values())
    };
  }).filter(project => project.competent_persons.length > 0);
}

/**
 * Utility functions for formatting data
 */
function useCompetentPersonUtils() {
  const t = useTranslations('cpList.fields');

  const formatDate = (dateString?: string) => {
    if (!dateString) return t('notSpecified');
    try {
      return format(new Date(dateString), 'dd/MM/yy');
    } catch {
      return t('invalidDate');
    }
  };

  const getCPTypeBadgeVariant = (cpType: string) => {
    switch (cpType) {
      case 'CP1':
        return 'default';
      case 'CP2':
        return 'secondary';
      case 'CP3':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const isExpired = (certExpDate?: string) => {
    return certExpDate ? new Date(certExpDate) < new Date() : false;
  };

  return { formatDate, getCPTypeBadgeVariant, isExpired };
}

/**
 * Admin Project Card Component
 */
function ProjectCard({ project }: { project: ProjectWithCPs }) {
  const { formatDate, getCPTypeBadgeVariant, isExpired } = useCompetentPersonUtils();

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg">{project.name}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {project.code} • {project.location}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Contractor: {project.contractor_name}
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            {project.competent_persons.length} CP{project.competent_persons.length !== 1 ? 's' : ''}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {project.competent_persons.map((cp) => (
            <div key={cp.id} className="p-3 bg-gray-50 rounded-lg border">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <UserCheck className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-sm">{cp.name}</span>
                </div>
                <Badge variant={getCPTypeBadgeVariant(cp.cp_type)} className="text-xs">
                  {cp.cp_type}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                <div>IC: {cp.ic_no}</div>
                <div className="flex items-center gap-1">
                  <Award className="h-3 w-3" />
                  {cp.pma_count} PMAs
                </div>
                
                {cp.phone_no && (
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {cp.phone_no}
                  </div>
                )}
                
                {cp.cert_exp_date && (
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span className={isExpired(cp.cert_exp_date) ? 'text-red-600 font-medium' : ''}>
                      Exp: {formatDate(cp.cert_exp_date)}
                    </span>
                  </div>
                )}
                
                {cp.cp_registeration_no && (
                  <div className="col-span-2 text-xs">
                    Reg: {cp.cp_registeration_no}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Summary Statistics Component
 */
function SummaryCards({ isAdmin, projects }: { isAdmin: boolean; projects?: ProjectWithCPs[] }) {
  const t = useTranslations('cpList');
  const { data: competentPersons = [], isLoading } = useCompetentPersonsStats();

  const stats = useMemo(() => {
    if (isAdmin && projects) {
      const totalProjects = projects.length;
      const totalCPs = projects.reduce((sum, p) => sum + p.competent_persons.length, 0);
      const byType = projects.reduce((acc, p) => {
        p.competent_persons.forEach(cp => {
          acc[cp.cp_type] = (acc[cp.cp_type] || 0) + 1;
        });
        return acc;
      }, {} as Record<string, number>);
      const expired = projects.reduce((sum, p) => 
        sum + p.competent_persons.filter(cp => 
          cp.cert_exp_date && new Date(cp.cert_exp_date) < new Date()
        ).length, 0
      );
      return { total: totalCPs, byType, expired, totalPMAs: 0, totalProjects };
    } else {
      const total = competentPersons.length;
      const byType = competentPersons.reduce(
        (acc, person) => {
          acc[person.cp_type] = (acc[person.cp_type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );
      const expired = competentPersons.filter((person) =>
        person.cert_exp_date
          ? new Date(person.cert_exp_date) < new Date()
          : false,
      ).length;
      const totalPMAs = competentPersons.reduce(
        (acc, person) => acc + person.no_of_pma,
        0,
      );
      return { total, byType, expired, totalPMAs };
    }
  }, [competentPersons, isAdmin, projects]);

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {isAdmin && 'totalProjects' in stats && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProjects}</div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('totalCPs')}</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('byType')}</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-1">
            {['CP1', 'CP2', 'CP3'].map((type) => (
              <div key={type} className="flex justify-between text-sm">
                <span>{type}:</span>
                <span className="font-medium">{stats.byType[type] || 0}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('expiredCerts')}
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-destructive">
            {stats.expired}
          </div>
        </CardContent>
      </Card>

      {!isAdmin && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('totalPMAs')}
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPMAs}</div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

/**
 * Main CP List Page Component
 */
export default function CPListPage() {
  const t = useTranslations();
  const tFields = useTranslations('cpList.fields');
  const { isContractor, isAdmin } = usePermissions();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingPerson, setEditingPerson] = useState<CompetentPerson | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [previewCertificate, setPreviewCertificate] = useState<{
    url: string;
    personName: string;
  } | null>(null);
  const { formatDate, getCPTypeBadgeVariant, isExpired } =
    useCompetentPersonUtils();

  // Admin data fetching
  const { data: adminProjects = [], isLoading: isAdminLoading, error: adminError } = useQuery({
    queryKey: ['admin-projects-with-cps'],
    queryFn: fetchProjectsWithCPs,
    enabled: isAdmin,
  });

  // Contractor data fetching
  const {
    data: searchResult,
    isLoading: isContractorLoading,
    error: contractorError,
    isFetching,
  } = useCompetentPersonsSearch(searchQuery);

  // Extract data from search result
  const competentPersons = searchResult?.data || [];
  const totalCount = searchResult?.count || 0;
  const hasCompetentPersons = competentPersons && competentPersons.length > 0;

  // Filter admin projects based on search
  const filteredProjects = useMemo(() => {
    if (!isAdmin || !searchQuery) return adminProjects;
    
    return adminProjects.filter(project => 
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.contractor_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.competent_persons.some(cp => 
        cp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cp.ic_no.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
  }, [adminProjects, searchQuery, isAdmin]);

  // Access control
  if (!isContractor && !isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <UserCheck className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('cpList.accessRestricted.title')}
            </h3>
            <p className="text-muted-foreground">
              {t('cpList.accessRestricted.description')}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isLoading = isAdmin ? isAdminLoading : isContractorLoading;
  const error = isAdmin ? adminError : contractorError;

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-8 bg-muted rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-6 bg-muted rounded w-3/4"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded w-5/6"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <UserCheck className="mx-auto h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('cpList.errorLoading.title')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('cpList.errorLoading.description')}: {error.message}
            </p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {isAdmin ? 'CP-List Dashboard' : t('cpList.title')}
          </h1>
          <p className="text-muted-foreground mt-2">
            {isAdmin 
              ? 'Track and verify Competent Persons across all projects'
              : t('cpList.description')
            }
          </p>
        </div>
        {isContractor && (
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            {t('cpList.addCP')}
          </Button>
        )}
      </div>

      {/* Summary Statistics */}
      <SummaryCards isAdmin={isAdmin} projects={adminProjects} />

      {isAdmin ? (
        // Admin View - Projects with CPs
        <>
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">
              Projects with CPs ({filteredProjects.length})
            </h2>
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search projects or CPs..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {filteredProjects.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredProjects.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <UserCheck className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-xl font-semibold mb-2">No Projects Found</h3>
                <p className="text-muted-foreground">
                  {searchQuery 
                    ? 'No projects match your search criteria.'
                    : 'No projects with competent persons found.'
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </>
      ) : (
        // Contractor View - CP Table
        hasCompetentPersons ? (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">
                Competent Persons ({competentPersons.length}/{totalCount})
              </h2>
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search competent persons..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {isFetching && (
                  <div className="absolute right-2.5 top-2.5">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                  </div>
                )}
              </div>
            </div>
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{tFields('name')}</TableHead>
                    <TableHead>{tFields('icNo')}</TableHead>
                    <TableHead>{tFields('cpType')}</TableHead>
                    <TableHead>{tFields('phone')}</TableHead>
                    <TableHead>{tFields('registrationNo')}</TableHead>
                    <TableHead>{tFields('certExpiry')}</TableHead>
                    <TableHead className="text-center">
                      {tFields('pmas')}
                    </TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {competentPersons.map((person) => (
                    <TableRow key={person.id} className="hover:bg-muted/50">
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 p-1.5 bg-primary/10 rounded-md">
                            <UserCheck className="h-4 w-4 text-primary" />
                          </div>
                          <span>{person.name}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {person.ic_no}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getCPTypeBadgeVariant(person.cp_type)}>
                          {person.cp_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {person.phone_no ? (
                          <div className="flex items-center space-x-2">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{person.phone_no}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {person.cp_registeration_no ? (
                          <div className="flex items-center space-x-2">
                            <Award className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">
                              {person.cp_registeration_no}
                            </span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {person.cert_exp_date ? (
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span
                              className={`text-sm ${
                                isExpired(person.cert_exp_date)
                                  ? 'text-destructive font-medium'
                                  : ''
                              }`}
                            >
                              {formatDate(person.cert_exp_date)}
                            </span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <FileText className="h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">
                            {person.no_of_pma}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {person.cp_registeration_cert && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                setPreviewCertificate({
                                  url: person.cp_registeration_cert!,
                                  personName: person.name,
                                })
                              }
                              title={tFields('viewCert')}
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                          )}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => setEditingPerson(person)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {tFields('editAction')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </div>
        </>
        ) : (
          // Empty state for contractor
          <Card>
            <CardContent className="text-center py-12">
              <UserCheck className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold mb-2">
                {t('cpList.noCompetentPersons.title')}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {t('cpList.noCompetentPersons.description')}
              </p>
              <Button onClick={() => setIsAddModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t('cpList.addFirstCP')}
              </Button>
            </CardContent>
          </Card>
        )
      )}

      {/* Add/Edit CP Modal - Only for contractors */}
      {isContractor && (
        <AddCPModal
          open={isAddModalOpen || !!editingPerson}
          onOpenChange={(open) => {
            if (!open) {
              setIsAddModalOpen(false);
              setEditingPerson(null);
            }
          }}
          editingPerson={editingPerson}
        />
      )}

      {/* Certificate Preview Modal */}
      {previewCertificate && (
        <CertificatePreviewModal
          open={!!previewCertificate}
          onOpenChange={(open) => {
            if (!open) {
              setPreviewCertificate(null);
            }
          }}
          certificateUrl={previewCertificate.url}
          personName={previewCertificate.personName}
        />
      )}
    </div>
  );
}
