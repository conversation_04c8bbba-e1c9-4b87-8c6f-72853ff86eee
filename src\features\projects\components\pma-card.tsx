'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUploadDropzone } from '@/components/ui/file-upload-dropzone';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useCurrentUserCompetentPersons } from '@/hooks/use-profile';
import {
  Calendar,
  FileText,
  MapPin,
  Trash2,
  Upload,
  User,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { UseFormReturn } from 'react-hook-form';
import { type PmasSchema } from '../schemas/project-schema';

// Helper function to safely extract error message
const getErrorMessage = (error: unknown): string => {
  if (typeof error === 'string') return error;
  if (error && typeof error === 'object' && 'message' in error) {
    const message = (error as { message: unknown }).message;
    return typeof message === 'string' ? message : String(message);
  }
  return 'Invalid input';
};

interface PmaCardProps {
  form: UseFormReturn<PmasSchema>;
  index: number;
  onRemove: () => void;
  canRemove: boolean;
}

/**
 * Individual PMA form card
 * Handles the form fields for a single PMA entry
 */
export function PmaCard({ form, index, onRemove, canRemove }: PmaCardProps) {
  // Get competent persons for the current user's contractor
  const { data: competentPersons, isLoading: loadingCompetentPersons } =
    useCurrentUserCompetentPersons();

  // Translations
  const t = useTranslations('pages.projects.create.form');

  return (
    <Card className="relative border-2 border-border/50 hover:border-border transition-colors duration-200">
      <CardHeader className="pb-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-t-lg">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            {t('pma.cardTitle', { number: index + 1 })}
          </CardTitle>
          {canRemove && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onRemove}
              className="text-destructive hover:text-destructive hover:bg-destructive/10"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              {t('actions.remove')}
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6 p-6">
        {/* PMA Number */}
        <div className="space-y-2">
          <Label
            htmlFor={`pma_number_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <div className="h-4 w-4 text-primary">#</div>
            {t('fields.pmaNumber.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Input
            id={`pma_number_${index}`}
            placeholder={t('fields.pmaNumber.placeholder')}
            className="text-sm"
            {...form.register(`pmas.${index}.pma_number` as const, {
              onChange: () => {},
              onBlur: (e) => {
                if (!e.target.value.trim()) {
                  form.setError(`pmas.${index}.pma_number`, {
                    type: 'manual',
                    message: 'PMA number is required',
                  });
                }
              },
            })}
          />
          {form.formState.errors.pmas?.[index]?.pma_number && (
            <p className="text-xs text-destructive">
              {getErrorMessage(form.formState.errors.pmas[index]?.pma_number)}
            </p>
          )}
        </div>

        {/* Competent Person Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <User className="h-4 w-4 text-primary" />
            {t('fields.competentPerson.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Select
            value={
              form.watch(`pmas.${index}.competent_person_id` as const) || ''
            }
            onValueChange={(value) => {
              form.setValue(
                `pmas.${index}.competent_person_id` as const,
                value,
              );
              form.trigger(`pmas.${index}.competent_person_id` as const);
            }}
            disabled={loadingCompetentPersons || !competentPersons?.length}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={
                  loadingCompetentPersons
                    ? 'Loading competent persons...'
                    : !competentPersons?.length
                      ? t('fields.competentPerson.noCompetentPersonsAvailable')
                      : t('fields.competentPerson.placeholder')
                }
              />
            </SelectTrigger>
            <SelectContent>
              {competentPersons?.map((cp) => (
                <SelectItem key={cp.id} value={cp.id}>
                  <div className="flex flex-col items-start">
                    <span className="font-medium text-left">{cp.name}</span>
                    <span className="text-xs text-muted-foreground text-left">
                      {cp.cp_type} •{' '}
                      {cp.cp_registeration_no || 'No registration number'}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {form.formState.errors.pmas?.[index]?.competent_person_id && (
            <p className="text-xs text-destructive">
              {getErrorMessage(
                form.formState.errors.pmas[index]?.competent_person_id,
              )}
            </p>
          )}
          {!loadingCompetentPersons && !competentPersons?.length && (
            <p className="text-xs text-muted-foreground">
              {t('fields.competentPerson.noCompetentPersonsNote')}
            </p>
          )}
        </div>

        {/* Inspection Date */}
        <div className="space-y-2">
          <Label
            htmlFor={`inspection_date_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <Calendar className="h-4 w-4 text-primary" />
            {t('fields.inspectionDate.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Input
            id={`inspection_date_${index}`}
            type="date"
            className="text-sm"
            {...form.register(`pmas.${index}.inspection_date` as const)}
          />
          {form.formState.errors.pmas?.[index]?.inspection_date && (
            <p className="text-xs text-destructive">
              {getErrorMessage(
                form.formState.errors.pmas[index]?.inspection_date,
              )}
            </p>
          )}
        </div>

        {/* Lift Inspection Date */}
        <div className="space-y-2">
          <Label
            htmlFor={`lift_installation_date_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <Calendar className="h-4 w-4 text-primary" />
            {t('fields.liftInstallationDate.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Input
            id={`lift_installation_date_${index}`}
            type="date"
            className="text-sm"
            {...form.register(`pmas.${index}.lift_installation_date` as const)}
          />
          {form.formState.errors.pmas?.[index]?.lift_installation_date && (
            <p className="text-xs text-destructive">
              {getErrorMessage(
                form.formState.errors.pmas[index]?.lift_installation_date,
              )}
            </p>
          )}
        </div>

        {/* PMA Expiry Date */}
        <div className="space-y-2">
          <Label
            htmlFor={`pma_expiry_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <Calendar className="h-4 w-4 text-primary" />
            {t('fields.pmaExpiry.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Input
            id={`pma_expiry_${index}`}
            type="date"
            className="text-sm"
            {...form.register(`pmas.${index}.expiry_date` as const)}
          />
          {form.formState.errors.pmas?.[index]?.expiry_date && (
            <p className="text-xs text-destructive">
              {getErrorMessage(form.formState.errors.pmas[index]?.expiry_date)}
            </p>
          )}
        </div>

        {/* LIF Location - Full Width */}
        <div className="space-y-2">
          <Label
            htmlFor={`lif_location_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <MapPin className="h-4 w-4 text-primary" />
            {t('fields.lifLocation.label')}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Textarea
            id={`lif_location_${index}`}
            placeholder={t('fields.lifLocation.placeholder')}
            className="min-h-[90px] text-sm resize-none"
            {...form.register(`pmas.${index}.location` as const)}
          />
          {form.formState.errors.pmas?.[index]?.location && (
            <p className="text-xs text-destructive">
              {getErrorMessage(form.formState.errors.pmas[index]?.location)}
            </p>
          )}
        </div>

        {/* File Upload Section */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Upload className="h-4 w-4 text-primary" />
            {t('fields.pmaCertificateFile.label')}{' '}
            <span className="text-destructive">*</span>
            <span className="text-xs text-muted-foreground font-normal">
              {t('fields.pmaCertificateFile.note')}
            </span>
          </Label>

          {/* Show file upload dropzone only if no file is selected */}
          {!form.watch(`pmas.${index}.file` as const) ||
          (form.watch(`pmas.${index}.file` as const) &&
            !(form.watch(`pmas.${index}.file` as const) as File).name) ? (
            <FileUploadDropzone
              onFilesChange={(files) => {
                const file = files[0];
                if (file) {
                  form.setValue(`pmas.${index}.file` as const, file);
                  form.trigger(`pmas.${index}.file` as const);
                }
              }}
              accept=".pdf,.jpg,.jpeg,.png"
              maxSize={10 * 1024 * 1024} // 10MB
              maxFiles={1}
              files={[]}
            />
          ) : (
            /* Show uploaded file */
            <div className="border-2 border-dashed border-border rounded-lg p-4 bg-muted/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-md">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-foreground">
                      {
                        (form.watch(`pmas.${index}.file` as const) as File)
                          ?.name
                      }
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {(
                        (form.watch(`pmas.${index}.file` as const) as File)
                          ?.size /
                        1024 /
                        1024
                      ).toFixed(2)}{' '}
                      MB
                    </p>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    FILE
                  </Badge>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    form.setValue(
                      `pmas.${index}.file` as const,
                      new File([], ''),
                    );
                    form.trigger(`pmas.${index}.file` as const);
                  }}
                  className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                  title={t('fields.pmaCertificateFile.removeFile')}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {form.formState.errors.pmas?.[index]?.file && (
            <p className="text-xs text-destructive">
              {getErrorMessage(form.formState.errors.pmas[index]?.file)}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
