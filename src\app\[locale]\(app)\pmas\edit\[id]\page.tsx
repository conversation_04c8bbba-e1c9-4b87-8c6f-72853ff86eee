'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { PmaForm } from '@/features/pma-management/components/pma-form';
import {
  usePMACertificate,
  useUpdatePMACertificate,
} from '@/features/pma-management/hooks/use-pma-certificates';
import { useProjectContext } from '@/providers/project-context';
import { Edit2, FileText } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

// Import the proper types
type PmaEditFormData = {
  pmas: {
    location?: string;
    pmaNumber?: string;
    inspection_date?: string; // matches form field name
    liftInstallationDate?: string;
    competentPersonId?: string;
    pmaExpiryDate: string;
    certificateFile?: File;
  }[];
};

export default function EditPma() {
  const { selectedProjectId } = useProjectContext();
  const params = useParams();
  const pmaId = params.id as string;
  const locale = params.locale as string;
  const router = useRouter();
  const t = useTranslations('pmaManagement.pages.edit');
  const tNav = useTranslations('navigation');

  // Fetch the PMA certificate data
  const { data: pma, isLoading: isLoadingPma } = usePMACertificate(pmaId);
  const { mutate, isPending } = useUpdatePMACertificate();

  // Prepare initial form data when PMA data is loaded
  const initialData = pma
    ? {
        pmas: [
          {
            location: pma.location || '',
            pmaNumber: pma.pma_number || '',
            inspection_date:
              typeof pma.inspection_date === 'string'
                ? pma.inspection_date
                : '',
            liftInstallationDate: pma.lift_installation_date || '',
            competentPersonId: pma.competent_person_id || '',
            pmaExpiryDate: pma.expiry_date || '',
            // No certificate file here as we're not uploading a new file in edit mode
          },
        ],
      }
    : undefined;

  const handleSubmit = async (data: PmaEditFormData) => {
    if (!selectedProjectId || !pma) {
      console.error('Project ID or PMA is not available.');
      return;
    }

    try {
      // Check if a new file is being uploaded
      let fileUrl = pma.file_url;

      if (data.pmas[0].certificateFile) {
        // Import the upload function
        const { uploadToOBS } = await import('@/lib/obs-upload');

        // Upload the new file
        fileUrl = await uploadToOBS({
          file: data.pmas[0].certificateFile,
          folder: `projects/${selectedProjectId}/pma-certificates`,
        });
        console.log('File uploaded successfully:', fileUrl);
      }

      // Prepare updates; by default update expiry and file
      const updates: Record<string, unknown> = {
        expiry_date: data.pmas[0].pmaExpiryDate,
        file_url: fileUrl,
      };

      // If full editing is enabled, include other fields
      if (editingEnabled) {
        updates.location = data.pmas[0].location ?? pma.location ?? '';
        updates.pma_number = data.pmas[0].pmaNumber ?? pma.pma_number ?? '';
        updates.inspection_date =
          (data.pmas[0] as { inspection_date?: string | null })
            .inspection_date ??
          pma.inspection_date ??
          null;
        updates.lift_installation_date =
          data.pmas[0].liftInstallationDate ??
          pma.lift_installation_date ??
          null;
        updates.competent_person_id =
          data.pmas[0].competentPersonId ?? pma.competent_person_id ?? null;
      }

      mutate(
        {
          id: pmaId,
          updates,
        },
        {
          onSuccess: () => {
            router.push(`/${locale}/pmas`);
          },
          onError: (error) => {
            console.error('Error updating PMA:', error);
          },
        },
      );
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  };

  const [editingEnabled, setEditingEnabled] = useState(false);

  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      {/* Enhanced Header with Breadcrumbs */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-slate-200/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link
                href={`/${locale}/pmas`}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>{tNav('pmas')}</span>
                </div>
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-primary font-medium">
                {t('breadcrumb')}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push(`/${locale}/pmas`)}
              >
                {t('cancel')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              {t('title')}
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              {t('description')}
            </p>
          </div>

          {/* Editing mode control */}
          {!isLoadingPma && (
            <div className="mb-6">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Edit2 className="h-4 w-4 mr-2" />
                    Enable full editing
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirm full edit</AlertDialogTitle>
                    <AlertDialogDescription>
                      You are about to enable editing for all fields. Please
                      ensure the updated information is accurate before saving.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => setEditingEnabled(true)}>
                      Proceed
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}

          {/* Form */}
          {isLoadingPma ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </div>
          ) : (
            <PmaForm
              onSubmit={handleSubmit}
              initialData={initialData}
              isLoading={isPending}
              isEditMode={true}
              disabledFields={
                editingEnabled
                  ? []
                  : [
                      'location',
                      'pmaNumber',
                      'inspectionDate',
                      'liftInstallationDate',
                      'competentPersonId',
                      'pmaExpiryDate',
                      'certificateFile',
                    ]
              }
            />
          )}
        </div>
      </div>
    </div>
  );
}
