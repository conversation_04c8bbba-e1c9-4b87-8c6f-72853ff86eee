import { useQuery } from '@tanstack/react-query';
import {
  getBatchedPayments,
  getBatchedPaymentById,
  getBatchedPaymentWithRecords,
  getBatchedPaymentSummary,
} from '../services/payment-records-api.service';
import type {
  BatchedPaymentsQueryParams,
  BatchedPaymentsResponse,
  BatchedPayment,
  BatchedPaymentWithRecords,
} from '../types/payment-records';

interface UseBatchedPaymentsParams {
  contractorId?: string;
  status?: BatchedPaymentsQueryParams['status'];
  dateFrom?: string;
  dateTo?: string;
  pmaCount?: number;
  page?: number;
  limit?: number;
  enabled?: boolean;
}

/**
 * Hook for fetching batched payments with filtering and pagination
 */
export function useBatchedPayments({
  contractorId,
  status,
  dateFrom,
  dateTo,
  pmaCount,
  page = 1,
  limit = 50,
  enabled = true,
}: UseBatchedPaymentsParams) {
  return useQuery({
    queryKey: [
      'batched-payments',
      contractorId,
      status,
      dateFrom,
      dateTo,
      pmaCount,
      page,
      limit,
    ],
    queryFn: (): Promise<BatchedPaymentsResponse> => {
      return getBatchedPayments({
        contractorId: contractorId!,
        status,
        dateFrom,
        dateTo,
        pmaCount,
        page,
        limit,
      });
    },
    enabled: enabled && !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

interface UseBatchedPaymentParams {
  id?: string;
  enabled?: boolean;
}

/**
 * Hook for fetching a single batched payment by ID
 */
export function useBatchedPayment({
  id,
  enabled = true,
}: UseBatchedPaymentParams) {
  return useQuery({
    queryKey: ['batched-payment', id],
    queryFn: (): Promise<BatchedPayment | null> => {
      return getBatchedPaymentById(id!);
    },
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

interface UseBatchedPaymentWithRecordsParams {
  id?: string;
  enabled?: boolean;
}

/**
 * Hook for fetching a batched payment with its associated payment records
 */
export function useBatchedPaymentWithRecords({
  id,
  enabled = true,
}: UseBatchedPaymentWithRecordsParams) {
  return useQuery({
    queryKey: ['batched-payment-with-records', id],
    queryFn: (): Promise<BatchedPaymentWithRecords | null> => {
      return getBatchedPaymentWithRecords(id!);
    },
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

interface UseBatchedPaymentSummaryParams {
  contractorId?: string;
  enabled?: boolean;
}

/**
 * Hook for fetching batched payment summary statistics for a contractor
 */
export function useBatchedPaymentSummary({
  contractorId,
  enabled = true,
}: UseBatchedPaymentSummaryParams) {
  return useQuery({
    queryKey: ['batched-payment-summary', contractorId],
    queryFn: () => {
      return getBatchedPaymentSummary(contractorId!);
    },
    enabled: enabled && !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for fetching batched payments that are currently pending
 */
export function usePendingBatchedPayments({
  contractorId,
  enabled = true,
}: {
  contractorId?: string;
  enabled?: boolean;
}) {
  return useBatchedPayments({
    contractorId,
    status: 'pending',
    enabled,
  });
}

/**
 * Hook for fetching paid batched payments
 */
export function usePaidBatchedPayments({
  contractorId,
  page = 1,
  limit = 20,
  enabled = true,
}: {
  contractorId?: string;
  page?: number;
  limit?: number;
  enabled?: boolean;
}) {
  return useBatchedPayments({
    contractorId,
    status: 'paid',
    page,
    limit,
    enabled,
  });
}

/**
 * Hook for fetching recent batched payments (last 30 days)
 */
export function useRecentBatchedPayments({
  contractorId,
  enabled = true,
}: {
  contractorId?: string;
  enabled?: boolean;
}) {
  const dateFrom = new Date();
  dateFrom.setDate(dateFrom.getDate() - 30);

  return useBatchedPayments({
    contractorId,
    dateFrom: dateFrom.toISOString().split('T')[0],
    enabled,
  });
}
