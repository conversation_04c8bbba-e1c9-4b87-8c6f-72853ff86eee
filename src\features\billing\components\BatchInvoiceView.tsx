'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Download, FileText, Package, Printer } from 'lucide-react';
import Image from 'next/image';
import { useBatchedPaymentWithRecords } from '../hooks/useBatchedPayments';

interface BatchInvoiceViewProps {
  batchId: string;
}

export function BatchInvoiceView({ batchId }: BatchInvoiceViewProps) {
  const {
    data: batchData,
    isLoading,
    error,
  } = useBatchedPaymentWithRecords({
    id: batchId,
    enabled: !!batchId,
  });

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    window.print();
  };

  if (isLoading) {
    return <BatchInvoiceSkeleton />;
  }

  if (error || !batchData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">
              Invoice Not Available
            </h2>
            <p className="text-muted-foreground">
              {error instanceof Error
                ? error.message
                : 'Unable to load batch invoice. Please try again later.'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate due date (30 days from created_at)
  const dueDate = new Date(batchData.created_at);
  dueDate.setDate(dueDate.getDate() + 30);

  return (
    <div className="min-h-screen bg-gray-50 p-4 print:bg-white print:p-0">
      <div className="max-w-4xl mx-auto">
        {/* Print/Download Actions - Hidden in print */}
        <div className="mb-6 print:hidden">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Payment Invoice</h1>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </div>
        </div>

        {/* Invoice Content */}
        <Card className="print:shadow-none print:border-none">
          <CardHeader className="text-center border-b print:border-b-2">
            <div className="space-y-4">
              <div className="flex justify-center">
                <Image
                  src="/Simple-Logo-Gradient.svg"
                  alt="SimPLE Logo"
                  width={120}
                  height={30}
                  className="print:w-[120px] print:h-[30px]"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Simple Project Lifecycle Enhancement
                </p>
                <p className="text-xs text-muted-foreground">INVOICE</p>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            {/* Invoice Header */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Invoice Details</h3>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="text-muted-foreground">Invoice Date:</span>
                    <br />
                    <span>{formatDate(batchData.created_at)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Due Date:</span>
                    <br />
                    <span>{formatDate(dueDate.toISOString())}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Information</h3>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="text-muted-foreground">Total PMAs:</span>
                    <br />
                    <div className="flex items-center gap-1">
                      <Package className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {batchData.total_pma_count} PMAs
                      </span>
                    </div>
                  </div>
                  {batchData.batch_bill_id && (
                    <div>
                      <span className="text-muted-foreground">Bill ID:</span>
                      <br />
                      <span className="font-mono text-xs">
                        {batchData.batch_bill_id}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            {/* PMA Numbers */}
            {/* <div>
              <h3 className="font-semibold mb-4">PMA Numbers Included</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {batchData.pma_numbers.map((pmaNumber) => (
                  <div
                    key={pmaNumber}
                    className="bg-gray-50 print:bg-gray-100 p-2 rounded text-sm font-mono text-center"
                  >
                    {pmaNumber}
                  </div>
                ))}
              </div>
            </div> */}

            {/* <Separator /> */}

            {/* Invoice Items */}
            <div>
              <h3 className="font-semibold mb-4">Invoice Items</h3>
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50 print:bg-gray-100">
                    <tr>
                      <th className="text-left p-3 font-medium">Description</th>
                      <th className="text-center p-3 font-medium">Quantity</th>
                      <th className="text-right p-3 font-medium">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t">
                      <td className="p-3">
                        <div>
                          <div className="font-medium">
                            PMA Subscription Fees
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Monthly subscription fees for{' '}
                            {batchData.total_pma_count} PMA certificates
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            {batchData.pma_numbers.join(', ')}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            Billing Period:{' '}
                            {formatMonthYear(batchData.created_at)}
                          </div>
                        </div>
                      </td>
                      <td className="p-3 text-center">
                        {batchData.total_pma_count}
                      </td>
                      <td className="p-3 text-right font-medium">
                        {formatCurrency(batchData.total_pma_count * 150)}
                      </td>
                    </tr>
                  </tbody>
                  <tfoot className="border-t bg-gray-50 print:bg-gray-100">
                    <tr>
                      <td className="p-3 font-medium" colSpan={2}>
                        Discount
                      </td>
                      <td className="p-3 text-right font-medium">
                        {formatCurrency(
                          batchData.total_pma_count * 150 -
                            batchData.total_amount,
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td className="p-3 font-semibold" colSpan={2}>
                        Total Amount Due
                      </td>
                      <td className="p-3 text-right font-bold text-lg">
                        {formatCurrency(batchData.total_amount)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Footer */}
            <div className="text-center text-xs text-muted-foreground pt-4 border-t">
              <p>
                This is a computer-generated invoice. No signature is required.
              </p>
              <p className="mt-1">
                Generated on {formatDate(new Date().toISOString())}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function BatchInvoiceSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-60" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
        </div>

        <Card>
          <CardHeader className="text-center border-b">
            <Skeleton className="h-6 w-24 mx-auto mb-2" />
            <Skeleton className="h-4 w-48 mx-auto mb-1" />
            <Skeleton className="h-3 w-32 mx-auto" />
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <Skeleton className="h-5 w-32" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-5 w-32" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Skeleton className="h-5 w-32" />
              <div className="grid grid-cols-4 gap-2">
                {Array.from({ length: 8 }).map((_, i) => (
                  <Skeleton key={i} className="h-8 w-full" />
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Skeleton className="h-5 w-32" />
              <div className="border rounded-lg p-4 space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-6 w-24" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-MY', {
    style: 'currency',
    currency: 'MYR',
  }).format(amount);
}

function formatDate(dateString: string): string {
  return new Intl.DateTimeFormat('en-MY', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString));
}

function formatMonthYear(dateString: string): string {
  return new Intl.DateTimeFormat('en-MY', {
    year: 'numeric',
    month: 'long',
  }).format(new Date(dateString));
}
