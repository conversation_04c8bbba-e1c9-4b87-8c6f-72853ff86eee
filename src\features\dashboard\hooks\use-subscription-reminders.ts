'use client';

import { useUserWithProfile } from '@/hooks';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import { useQuery } from '@tanstack/react-query';

type SubscriptionStatus = Database['public']['Enums']['subscription_status'];

interface TrialSubscription {
  id: string;
  status: SubscriptionStatus;
  trial_ends_at: string | null;
  calculated_amount: number;
  currency: string;
  created_at: string;
  pma_certificates: {
    pma_number: string | null;
    project_id: string | null;
    projects: {
      name: string;
    } | null;
  } | null;
}

export interface SubscriptionReminderData {
  trialSubscriptions: TrialSubscription[];
  expiredTrialCount: number;
  expiringSoonCount: number;
  totalTrialAmount: number;
}

/**
 * Hook to get subscription reminders for trial users
 */
export function useSubscriptionReminders() {
  const { data: user, isLoading: userLoading } = useUserWithProfile();

  return useQuery({
    queryKey: ['subscription-reminders', user?.id],
    queryFn: async (): Promise<SubscriptionReminderData> => {
      if (!user?.id) {
        return {
          trialSubscriptions: [],
          expiredTrialCount: 0,
          expiringSoonCount: 0,
          totalTrialAmount: 0,
        };
      }

      // Get user profile to get contractor_id
      const { data: userProfile } = await supabase
        .from('users')
        .select('contractor_id, user_role')
        .eq('id', user.id)
        .single();

      if (!userProfile?.contractor_id) {
        return {
          trialSubscriptions: [],
          expiredTrialCount: 0,
          expiringSoonCount: 0,
          totalTrialAmount: 0,
        };
      }

      // Get only the first PMA subscription (earliest created) - one per contractor
      const { data: subscription, error } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          id,
          status,
          trial_ends_at,
          calculated_amount,
          currency,
          created_at,
          pma_certificates!inner (
            pma_number,
            project_id,
            projects (
              name
            )
          )
        `,
        )
        .eq('contractor_id', userProfile.contractor_id)
        .in('status', ['trial', 'pending_payment'])
        .order('created_at', { ascending: true })
        .limit(1)
        .maybeSingle();

      if (error) {
        console.error('Error fetching subscription reminders:', error);
        throw error;
      }

      const trialSubscriptions: TrialSubscription[] = subscription
        ? [subscription as TrialSubscription]
        : [];

      const now = new Date();
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(now.getDate() + 3);

      let expiredTrialCount = 0;
      let expiringSoonCount = 0;
      let totalTrialAmount = 0;

      trialSubscriptions.forEach((sub) => {
        if (sub.trial_ends_at) {
          const trialEndDate = new Date(sub.trial_ends_at);

          if (trialEndDate <= now) {
            expiredTrialCount += 1;
          } else if (trialEndDate <= threeDaysFromNow) {
            expiringSoonCount += 1;
          }
        }

        if (sub.status === 'pending_payment') {
          totalTrialAmount += sub.calculated_amount;
        }
      });

      return {
        trialSubscriptions,
        expiredTrialCount,
        expiringSoonCount,
        totalTrialAmount,
      };
    },
    enabled: !userLoading && !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes (more frequent than other dashboard data)
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}
