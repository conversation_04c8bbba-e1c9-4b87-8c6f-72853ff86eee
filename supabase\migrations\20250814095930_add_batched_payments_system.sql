-- ================================
-- BATCHED PAYMENTS SYSTEM
-- Migration to add batched payment support for consolidated multiple PMA payments
-- ================================

-- ================================
-- NEW TABLE: PMA BATCHED PAYMENTS
-- ================================

CREATE TABLE public.pma_batched_payments (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    batch_bill_id text UNIQUE,
    contractor_id uuid NOT NULL,
    total_amount decimal(10,2) NOT NULL,
    total_pma_count integer NOT NULL,
    pma_numbers text[] NOT NULL,
    status public.payment_status DEFAULT 'pending' NOT NULL,
    paid_at timestamp with time zone,
    failure_reason text,
    billplz_response jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_pma_batched_payments_contractor_id 
        FOREIGN KEY (contractor_id) REFERENCES public.contractors(id) ON DELETE CASCADE,
        
    -- Business logic constraints
    CONSTRAINT chk_batched_payment_amount_positive CHECK (total_amount > 0),
    CONSTRAINT chk_batched_payment_pma_count_positive CHECK (total_pma_count > 0),
    CONSTRAINT chk_batched_payment_pma_count_matches_array CHECK (total_pma_count = array_length(pma_numbers, 1)),
    CONSTRAINT chk_batched_paid_at_logic CHECK (
        (status = 'paid' AND paid_at IS NOT NULL) OR
        (status != 'paid' AND paid_at IS NULL)
    )
);

-- ================================
-- MODIFY EXISTING TABLE: PMA PAYMENT RECORDS
-- Add optional foreign key to batched payments
-- ================================

ALTER TABLE public.pma_payment_records 
ADD COLUMN batched_payment_id uuid;

-- Add foreign key constraint
ALTER TABLE public.pma_payment_records
ADD CONSTRAINT fk_pma_payment_records_batched_payment_id 
    FOREIGN KEY (batched_payment_id) REFERENCES public.pma_batched_payments(id) ON DELETE SET NULL;

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================

-- Batched payments indexes
CREATE INDEX idx_pma_batched_payments_contractor_status 
    ON public.pma_batched_payments(contractor_id, status);
CREATE INDEX idx_pma_batched_payments_batch_bill_id 
    ON public.pma_batched_payments(batch_bill_id) 
    WHERE batch_bill_id IS NOT NULL;
CREATE INDEX idx_pma_batched_payments_created_at 
    ON public.pma_batched_payments(created_at DESC);
CREATE INDEX idx_pma_batched_payments_pma_count 
    ON public.pma_batched_payments(total_pma_count);

-- Payment records batch relationship index
CREATE INDEX idx_pma_payment_records_batched_payment_id 
    ON public.pma_payment_records(batched_payment_id) 
    WHERE batched_payment_id IS NOT NULL;

-- Composite index for efficient batch queries
CREATE INDEX idx_pma_payment_records_batch_status 
    ON public.pma_payment_records(batched_payment_id, status) 
    WHERE batched_payment_id IS NOT NULL;

-- ================================
-- ROW LEVEL SECURITY (RLS)
-- ================================

-- RLS disabled for pma_batched_payments to allow service operations

-- ================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ================================

-- Trigger for pma_batched_payments updated_at
CREATE TRIGGER trigger_update_pma_batched_payments_updated_at 
    BEFORE UPDATE ON public.pma_batched_payments
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ================================
-- COMMENTS FOR DOCUMENTATION
-- ================================

-- Table comments
COMMENT ON TABLE public.pma_batched_payments IS 'Consolidated payment records when multiple PMAs are paid together';

-- Column comments
COMMENT ON COLUMN public.pma_batched_payments.batch_bill_id IS 'BillPlz bill ID for the consolidated payment';
COMMENT ON COLUMN public.pma_batched_payments.total_amount IS 'Total amount for all PMAs in MYR';
COMMENT ON COLUMN public.pma_batched_payments.total_pma_count IS 'Number of PMAs in this batch';
COMMENT ON COLUMN public.pma_batched_payments.pma_numbers IS 'Array of PMA numbers included in this batch for quick reference';
COMMENT ON COLUMN public.pma_batched_payments.status IS 'Payment status of the entire batch';
COMMENT ON COLUMN public.pma_batched_payments.billplz_response IS 'Complete BillPlz API response for debugging';

COMMENT ON COLUMN public.pma_payment_records.batched_payment_id IS 'Optional reference to batched payment when this record is part of a batch';

-- ================================
-- BATCHED PAYMENTS SYSTEM READY
-- ================================

-- ================================
-- END OF MIGRATION  
-- ================================