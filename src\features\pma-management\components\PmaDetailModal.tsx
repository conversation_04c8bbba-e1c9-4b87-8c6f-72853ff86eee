'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { format, parseISO } from 'date-fns';
import {
  Calendar,
  Download,
  ExternalLink,
  FileText,
  MapPin,
  User,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';
import { PMACertificate } from '../types/pma-certificate';

interface PmaDetailModalProps {
  pma: PMACertificate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PmaDetailModal({
  pma,
  open,
  onOpenChange,
}: PmaDetailModalProps) {
  const tPma = useTranslations('pmaManagement');
  const tCommon = useTranslations('common');
  const [showCertificatePreview, setShowCertificatePreview] = useState(false);
  const [pdfViewMethod, setPdfViewMethod] = useState<
    'iframe' | 'embed' | 'object' | 'proxy' | 'fallback'
  >('iframe');

  if (!pma) return null;

  // Calculate status based on expiry date
  const getStatus = () => {
    if (!pma.expiry_date) return 'active';

    const now = new Date();
    const expiry = parseISO(pma.expiry_date);
    const daysDiff = Math.ceil(
      (expiry.getTime() - now.getTime()) / (1000 * 3600 * 24),
    );

    if (daysDiff <= 0) return 'expired';
    if (daysDiff <= 30) return 'expiring_soon';
    return 'active';
  };

  const status = getStatus();

  const getStatusBadge = () => {
    let badgeClassName = '';
    switch (status) {
      case 'active':
        badgeClassName =
          'bg-emerald-50 text-emerald-700 ring-1 ring-inset ring-emerald-600/20';
        break;
      case 'expiring_soon':
        badgeClassName =
          'bg-amber-50 text-amber-700 ring-1 ring-inset ring-amber-600/20';
        break;
      case 'expired':
        badgeClassName =
          'bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20';
        break;
    }
    return (
      <Badge className={cn('font-medium capitalize', badgeClassName)}>
        {tPma(`status.${status}`)}
      </Badge>
    );
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return tCommon('notSet');
    try {
      return format(parseISO(dateString), 'dd MMM yyyy, HH:mm');
    } catch {
      return dateString;
    }
  };

  const handleDownloadCertificate = () => {
    if (pma.file_url) {
      try {
        // Create a link to trigger download
        const link = document.createElement('a');
        link.href = pma.file_url;
        link.download = `PMA_Certificate_${pma.pma_number || pma.id}.${getFileExtension(pma.file_url) || 'pdf'}`;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        console.error('Error downloading certificate:', error);
        // Fallback to opening in new tab
        window.open(pma.file_url, '_blank');
      }
    }
  };

  const handleViewCertificate = () => {
    if (pma.file_url) {
      setPdfViewMethod('proxy'); // Start with proxy method for PDFs
      setShowCertificatePreview(true);
    }
  };

  // Function to get a properly formatted PDF URL for preview
  const getPdfPreviewUrl = (url: string) => {
    try {
      // For Supabase storage, use proper parameters to force inline viewing
      if (url.includes('supabase') || url.includes('storage')) {
        const urlObj = new URL(url);
        urlObj.searchParams.delete('download'); // Remove any existing download param
        urlObj.searchParams.set('response-content-disposition', 'inline');
        return urlObj.toString();
      }
      return url;
    } catch {
      return url;
    }
  };

  // Get proxy URL for PDF preview (fallback method)
  const getProxyPdfUrl = (url: string) => {
    const encodedUrl = encodeURIComponent(url);
    return `/api/preview-pdf?url=${encodedUrl}`;
  };

  // Function to try different PDF viewing methods
  const tryNextPdfMethod = () => {
    switch (pdfViewMethod) {
      case 'iframe':
        setPdfViewMethod('embed');
        break;
      case 'embed':
        setPdfViewMethod('object');
        break;
      case 'object':
        setPdfViewMethod('proxy');
        break;
      case 'proxy':
        setPdfViewMethod('fallback');
        break;
      default:
        setPdfViewMethod('fallback');
    }
  };

  const getFileExtension = (url: string) => {
    try {
      const urlPath = new URL(url).pathname;
      return urlPath.split('.').pop()?.toLowerCase() || '';
    } catch {
      return '';
    }
  };

  const isImageFile = (url: string) => {
    const ext = getFileExtension(url);
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext);
  };

  const isPdfFile = (url: string) => {
    const ext = getFileExtension(url);
    return ext === 'pdf';
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-semibold text-gray-900">
                  {tPma('detail.title')}
                </span>
                <span className="text-sm text-gray-500">
                  {pma.pma_number || tCommon('notSet')}
                </span>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Status and Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    {tPma('table.status')}
                  </span>
                </div>
                <div className="ml-6">{getStatusBadge()}</div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    {tPma('table.expiryDate')}
                  </span>
                </div>
                <div className="ml-6">
                  <span className="text-sm text-gray-900">
                    {pma.expiry_date
                      ? format(parseISO(pma.expiry_date), 'dd MMM yyyy')
                      : tCommon('notSet')}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* PMA Number */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('detail.pmaNumber')}
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {pma.pma_number || tCommon('notSet')}
                </span>
              </div>
            </div>

            <Separator />

            {/* Dates Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    {tPma('detail.inspectionDate')}
                  </span>
                </div>
                <div className="ml-6">
                  <span className="text-sm text-gray-900">
                    {pma.inspection_date
                      ? format(parseISO(pma.inspection_date), 'dd MMM yyyy')
                      : tCommon('notSet')}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    {tPma('detail.liftInstallationDate')}
                  </span>
                </div>
                <div className="ml-6">
                  <span className="text-sm text-gray-900">
                    {pma.lift_installation_date
                      ? format(
                          parseISO(pma.lift_installation_date),
                          'dd MMM yyyy',
                        )
                      : tCommon('notSet')}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Location Information */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('form.location')}
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {pma.location || tCommon('notSet')}
                </span>
                {pma.state && (
                  <span className="text-sm text-gray-500 ml-2">
                    ({pma.state})
                  </span>
                )}
              </div>
            </div>

            <Separator />

            {/* Competent Person Information */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('form.competentPerson')}
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {pma.competent_person_name || tCommon('notSet')}
                </span>
              </div>
            </div>

            <Separator />

            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    {tPma('detail.dateCreated')}
                  </span>
                </div>
                <div className="ml-6">
                  <span className="text-sm text-gray-900">
                    {formatDate(pma.created_at)}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    {tPma('detail.lastUpdated')}
                  </span>
                </div>
                <div className="ml-6">
                  <span className="text-sm text-gray-900">
                    {formatDate(pma.updated_at)}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Certificate File */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('form.pdfUpload')}
                </span>
              </div>
              <div className="ml-6">
                {pma.file_url ? (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="p-1 bg-blue-50 rounded">
                        <FileText className="h-3 w-3 text-blue-600" />
                      </div>
                      <span>
                        {isPdfFile(pma.file_url)
                          ? 'PDF Certificate'
                          : isImageFile(pma.file_url)
                            ? 'Image Certificate'
                            : 'Certificate File'}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleViewCertificate}
                        className="flex items-center gap-2"
                      >
                        <ExternalLink className="h-4 w-4" />
                        {tPma('detail.viewCertificate')}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <span className="text-sm text-gray-500">
                    {tPma('detail.noCertificateUploaded')}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {tCommon('close')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Certificate Preview Modal */}
      <Dialog
        open={showCertificatePreview}
        onOpenChange={setShowCertificatePreview}
      >
        <DialogContent className="max-w-4xl max-h-[95vh] p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>
              Certificate Preview - {pma.pma_number || 'PMA Certificate'}
            </DialogTitle>
          </DialogHeader>
          <div className="relative h-full">
            {/* Header with controls */}
            <div className="absolute top-0 right-0 z-10 flex items-center gap-2 p-4 bg-white/90 backdrop-blur-sm rounded-bl-lg">
              {/* Debug indicator - remove in production */}
              {process.env.NODE_ENV === 'development'}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadCertificate}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {tPma('detail.downloadCertificate')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCertificatePreview(false)}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                {tCommon('close')}
              </Button>
            </div>

            {/* Certificate Content */}
            <div className="h-[90vh] w-full">
              {pma?.file_url && (
                <>
                  {isPdfFile(pma.file_url) ? (
                    <div className="h-full w-full">
                      {pdfViewMethod === 'iframe' && (
                        <iframe
                          src={`${getPdfPreviewUrl(pma.file_url)}#view=FitH&toolbar=0`}
                          className="w-full h-full border-0"
                          title="Certificate Preview"
                          onError={() => {
                            console.log('Iframe failed, trying embed method');
                            tryNextPdfMethod();
                          }}
                        />
                      )}

                      {pdfViewMethod === 'embed' && (
                        <embed
                          src={getPdfPreviewUrl(pma.file_url)}
                          type="application/pdf"
                          className="w-full h-full"
                          title="Certificate Preview"
                          onError={() => {
                            console.log('Embed failed, trying object method');
                            tryNextPdfMethod();
                          }}
                        />
                      )}

                      {pdfViewMethod === 'object' && (
                        <object
                          data={getPdfPreviewUrl(pma.file_url)}
                          type="application/pdf"
                          className="w-full h-full"
                          title="Certificate Preview"
                          onError={() => {
                            console.log('Object failed, showing fallback');
                            tryNextPdfMethod();
                          }}
                        >
                          <div className="h-full flex items-center justify-center">
                            <Button
                              onClick={tryNextPdfMethod}
                              variant="outline"
                            >
                              Try Alternative Method
                            </Button>
                          </div>
                        </object>
                      )}

                      {pdfViewMethod === 'proxy' && (
                        <iframe
                          src={getProxyPdfUrl(pma.file_url)}
                          className="w-full h-full border-0"
                          title="Certificate Preview (Proxy)"
                          onError={() => {
                            console.log(
                              'Proxy method failed, showing fallback',
                            );
                            tryNextPdfMethod();
                          }}
                        />
                      )}

                      {pdfViewMethod === 'fallback' && (
                        <div className="h-full flex items-center justify-center bg-gray-50">
                          <div className="text-center max-w-md">
                            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-600 mb-4">
                              {tPma('detail.pdfPreviewUnavailable')}
                            </p>
                            <p className="text-sm text-gray-500 mb-6">
                              This PDF cannot be previewed in the browser. You
                              can download it or try opening in a new tab.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-2 justify-center">
                              <Button
                                onClick={handleDownloadCertificate}
                                className="flex items-center gap-2"
                              >
                                <Download className="h-4 w-4" />
                                {tPma('detail.downloadCertificate')}
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() =>
                                  pma.file_url &&
                                  window.open(pma.file_url, '_blank')
                                }
                                className="flex items-center gap-2"
                              >
                                <ExternalLink className="h-4 w-4" />
                                Open in New Tab
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setPdfViewMethod('iframe');
                                }}
                                className="flex items-center gap-2"
                              >
                                🔄 Try Again
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : isImageFile(pma.file_url) ? (
                    <div className="h-full flex items-center justify-center bg-gray-50">
                      <Image
                        src={pma.file_url}
                        alt="Certificate"
                        width={800}
                        height={600}
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                  ) : (
                    <div className="h-full flex items-center justify-center bg-gray-50">
                      <div className="text-center">
                        <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600 mb-4">
                          {tPma('detail.cannotPreviewFile')}
                        </p>
                        <Button
                          onClick={handleDownloadCertificate}
                          className="flex items-center gap-2"
                        >
                          <Download className="h-4 w-4" />
                          {tPma('detail.downloadCertificate')}
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
