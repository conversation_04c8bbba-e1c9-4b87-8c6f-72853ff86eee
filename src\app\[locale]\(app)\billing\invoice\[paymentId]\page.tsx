import { PaymentInvoiceView } from '@/features/billing/components/PaymentInvoiceView';
import { Metadata } from 'next';

interface PageProps {
  params: Promise<{
    paymentId: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { paymentId } = await params;
  return {
    title: `Payment Invoice - ${paymentId}`,
    description: 'Payment invoice for SimPLE billing',
    robots: 'noindex, nofollow', // Don't index invoice pages
  };
}

export default async function PaymentInvoicePage({ params }: PageProps) {
  const { paymentId } = await params;
  return <PaymentInvoiceView paymentId={paymentId} />;
}
