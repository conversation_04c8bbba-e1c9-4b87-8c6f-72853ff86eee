'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { format, parseISO } from 'date-fns';
import {
  Activity,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Phone,
  Settings,
  User,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import type {
  MAINTENANCE_STATUS,
  OPERATION_LOG_TYPES,
} from '../schemas/create-maintenance-log';
import type { MaintenanceLog } from '../types/table';
import { MaintenanceTypeBadge } from './MaintenanceTypeBadge';

interface MaintenanceLogDetailModalProps {
  maintenanceLog: MaintenanceLog | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function MaintenanceLogDetailModal({
  maintenanceLog,
  open,
  onOpenChange,
}: MaintenanceLogDetailModalProps) {
  const t = useTranslations('pages.maintenanceLogs');
  const tCommon = useTranslations('common');

  if (!maintenanceLog) return null;

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'dd MMM yyyy, HH:mm');
    } catch {
      return format(new Date(dateString), 'dd MMM yyyy, HH:mm');
    }
  };

  const getStatusDisplay = (status: (typeof MAINTENANCE_STATUS)[number]) => {
    const statusConfig = {
      'in service': {
        icon: CheckCircle,
        className: 'text-emerald-600',
        bgClassName: 'bg-emerald-50 border-emerald-200',
        textClassName: 'text-emerald-700',
      },
      'out of service': {
        icon: XCircle,
        className: 'text-red-600',
        bgClassName: 'bg-red-50 border-red-200',
        textClassName: 'text-red-700',
      },
    };

    const config = statusConfig[status] || {
      icon: Activity,
      className: 'text-gray-600',
      bgClassName: 'bg-gray-50 border-gray-200',
      textClassName: 'text-gray-700',
    };

    const IconComponent = config.icon;

    return (
      <div
        className={cn(
          'flex items-center gap-3 p-4 rounded-lg border',
          config.bgClassName,
        )}
      >
        <div className={cn('p-2 rounded-full bg-white', config.className)}>
          <IconComponent className="h-5 w-5" />
        </div>
        <div>
          <span className={cn('font-medium', config.textClassName)}>
            {t(`status.${status}`, { default: status })}
          </span>
          <p className="text-sm text-gray-600">Current status</p>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex flex-col">
              <span className="text-lg font-semibold text-gray-900">
                {t('detail.title')}
              </span>
              <span className="text-sm text-gray-500">
                {formatDate(maintenanceLog.created_at)}
              </span>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Status
              </h3>
              {getStatusDisplay(maintenanceLog.status)}
            </div>

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Operation Type
              </h3>
              <div className="p-4 bg-gray-50 rounded-lg border">
                <MaintenanceTypeBadge
                  type={
                    maintenanceLog.operation_log_type as (typeof OPERATION_LOG_TYPES)[number]
                  }
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Date and Time Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  Log Date
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {format(new Date(maintenanceLog.log_date), 'dd MMM yyyy')}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  Created At
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {formatDate(maintenanceLog.created_at)}
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Contractor Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Building className="h-4 w-4" />
              Contractor Information
            </h3>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium text-gray-900">
                    {maintenanceLog.contractor_name}
                  </p>
                  <p className="text-sm text-gray-600">Contractor</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium text-gray-900">
                    {maintenanceLog.person_in_charge_name}
                  </p>
                  <p className="text-sm text-gray-600">Person in Charge</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium text-gray-900">
                    {maintenanceLog.person_in_charge_phone}
                  </p>
                  <p className="text-sm text-gray-600">Contact Number</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* PMA Certificate */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                PMA Certificate
              </span>
            </div>
            <div className="ml-6">
              {maintenanceLog.pma_number ? (
                <Badge
                  variant="outline"
                  className="font-mono text-xs bg-gradient-to-r from-slate-50 to-gray-50 text-slate-700 border-slate-200"
                >
                  {maintenanceLog.pma_number}
                </Badge>
              ) : (
                <span className="text-sm text-gray-500 italic">
                  No certificate assigned
                </span>
              )}
            </div>
          </div>

          <Separator />

          {/* Description */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Description
              </span>
            </div>
            <div className="ml-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-900 leading-relaxed">
                  {maintenanceLog.description}
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* System Information */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                System Information
              </span>
            </div>
            <div className="ml-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-gray-500">Created By</p>
                <p className="text-sm font-medium text-gray-900">
                  {maintenanceLog.created_by || 'System'}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Record ID</p>
                <p className="text-sm font-mono text-gray-900">
                  {maintenanceLog.id}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {tCommon('close')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
