'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { ContractorBillingData } from '../types/project-grouped-billing';
import {
  useContractorBulkPayment,
  prepareContractorBulkPaymentData,
} from '../hooks/useContractorBulkPayment';
import type { PaymentMethodSelection } from './PaymentMethodSelector';
import { PaymentMethodSelector } from './PaymentMethodSelector';
import {
  AlertTriangle,
  Building2,
  DollarSign,
  Shield,
  Zap,
} from 'lucide-react';
import { useState, useCallback } from 'react';
import { useUser } from '@/hooks/use-auth';

export interface ContractorBulkPaymentModalProps {
  contractorData: ContractorBillingData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPaymentSuccess?: () => void;
}

export function ContractorBulkPaymentModal({
  contractorData,
  open,
  onOpenChange,
  onPaymentSuccess: _onPaymentSuccess,
}: ContractorBulkPaymentModalProps) {
  const [paymentSelection, setPaymentSelection] =
    useState<PaymentMethodSelection | null>(null);
  const { processBulkPayment, isProcessing, error } =
    useContractorBulkPayment();
  const { data: user } = useUser();

  // Memoize the selection change handler to prevent infinite re-renders
  const handleSelectionChange = useCallback(
    (selection: PaymentMethodSelection | null) => {
      setPaymentSelection(selection);
    },
    [],
  );

  const bulkPaymentData = prepareContractorBulkPaymentData(contractorData);
  const hasOutstandingPayments = bulkPaymentData.total_pmas_count > 0;

  const handlePayAll = async () => {
    if (!paymentSelection || !user?.id) {
      return;
    }

    const request = {
      contractor_id: contractorData.contractor_id,
      user_id: user.id,
      subscription_ids: bulkPaymentData.outstanding_subscriptions.map(
        (s) => s.id,
      ),
      total_amount: bulkPaymentData.total_amount,
      payment_method: paymentSelection.type,
      ...(paymentSelection.cardToken && {
        card_token: paymentSelection.cardToken,
      }),
    };

    const result = await processBulkPayment(request);

    // Handle different payment methods
    if (result) {
      if (result.payment_method === 'billplz_redirect' && result.payment_url) {
        // Redirect to BillPlz for redirect payments
        window.location.href = result.payment_url;
      } else if (result.payment_method === 'card_charge') {
        // Card payment completed, close modal and show success
        onOpenChange(false);
        // Could trigger success callback here
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'suspended':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Manage All Payments
          </DialogTitle>
          <DialogDescription>
            {contractorData.contractor_name} - Pay for all outstanding PMAs
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 pr-2">
          {/* Payment Summary */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Payment Summary</h3>
              {bulkPaymentData.total_pmas_count > 0 && (
                <Badge variant="destructive" className="text-xs">
                  <Zap className="h-3 w-3 mr-1" />
                  {bulkPaymentData.total_pmas_count} PMAs require payment
                </Badge>
              )}
            </div>

            {!hasOutstandingPayments ? (
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  All PMAs are up to date. No payments required at this time.
                </AlertDescription>
              </Alert>
            ) : (
              <>
                {/* Outstanding PMAs Summary */}
                <div className="p-4 bg-primary/5 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-primary" />
                      <span className="font-medium">Outstanding PMAs</span>
                    </div>
                    <div className="text-2xl font-bold text-primary">
                      {bulkPaymentData.total_pmas_count} PMA
                      {bulkPaymentData.total_pmas_count !== 1 ? 's' : ''}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Payment required for all outstanding PMAs
                  </p>
                </div>

                {/* Payment Breakdown */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {bulkPaymentData.payment_breakdown.trial > 0 && (
                    <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="text-xl font-bold text-blue-600">
                        {bulkPaymentData.payment_breakdown.trial}
                      </div>
                      <div className="text-xs text-muted-foreground">Trial</div>
                    </div>
                  )}

                  {bulkPaymentData.payment_breakdown.grace_period > 0 && (
                    <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <div className="text-xl font-bold text-yellow-600">
                        {bulkPaymentData.payment_breakdown.grace_period}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Grace Period
                      </div>
                    </div>
                  )}

                  {bulkPaymentData.payment_breakdown.active_overdue > 0 && (
                    <div className="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
                      <div className="text-xl font-bold text-orange-600">
                        {bulkPaymentData.payment_breakdown.active_overdue}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Overdue
                      </div>
                    </div>
                  )}

                  {bulkPaymentData.payment_breakdown.suspended > 0 && (
                    <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
                      <div className="text-xl font-bold text-red-600">
                        {bulkPaymentData.payment_breakdown.suspended}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Suspended
                      </div>
                    </div>
                  )}
                </div>

                {/* PMA List */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                    PMAs Included in Payment ({bulkPaymentData.total_pmas_count}
                    )
                  </h4>

                  <div className="h-[200px] w-full border rounded-md p-4 overflow-y-auto">
                    <div className="space-y-2">
                      {bulkPaymentData.outstanding_subscriptions.map(
                        (subscription, index) => {
                          const project = contractorData.projects.find((p) =>
                            p.pma_subscriptions.some(
                              (s) => s.id === subscription.id,
                            ),
                          );

                          return (
                            <div
                              key={subscription.id}
                              className="flex items-center justify-between p-2 bg-muted/30 rounded"
                            >
                              <div className="flex items-center gap-2">
                                <span className="text-xs font-mono bg-muted px-1 rounded">
                                  {index + 1}
                                </span>
                                <div>
                                  <p className="text-sm font-medium">
                                    {project?.project_name}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    PMA:{' '}
                                    {subscription.pma_certificates
                                      ?.pma_number || 'Unknown PMA'}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge
                                  className={cn(
                                    'text-xs',
                                    getStatusColor(subscription.status),
                                  )}
                                >
                                  {subscription.status
                                    .replace('_', ' ')
                                    .toUpperCase()}
                                </Badge>
                                {/* Amount hidden as per requirements */}
                                {/* <span className="text-sm font-medium">
                                  RM {(subscription.amount || 0).toFixed(2)}
                                </span> */}
                              </div>
                            </div>
                          );
                        },
                      )}
                    </div>
                  </div>
                </div>

                {/* Payment Method Selection */}
                <PaymentMethodSelector
                  contractorId={contractorData.contractor_id}
                  onSelectionChange={handleSelectionChange}
                  disabled={isProcessing}
                />

                {/* Error Display */}
                {error && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {error instanceof Error
                        ? error.message
                        : 'Payment failed. Please try again.'}
                    </AlertDescription>
                  </Alert>
                )}
              </>
            )}
          </div>
        </div>

        <Separator className="flex-shrink-0" />

        {/* Action Buttons - Fixed Footer */}
        <div className="flex-shrink-0 flex justify-end gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isProcessing}
          >
            Cancel
          </Button>

          {hasOutstandingPayments && (
            <Button
              onClick={handlePayAll}
              disabled={isProcessing || !paymentSelection || !user?.id}
              className="bg-primary hover:bg-primary/90"
            >
              {isProcessing ? <>Processing Payment...</> : <>Pay All PMAs</>}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
