import { BatchReceiptView } from '@/features/billing/components/BatchReceiptView';
import { Metadata } from 'next';

interface PageProps {
  params: Promise<{
    batchId: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { batchId } = await params;
  return {
    title: `Batch Receipt - ${batchId}`,
    description: 'Batch payment receipt for SimPLE billing',
    robots: 'noindex, nofollow', // Don't index receipt pages
  };
}

export default async function BatchReceiptPage({ params }: PageProps) {
  const { batchId } = await params;
  return <BatchReceiptView batchId={batchId} />;
}
