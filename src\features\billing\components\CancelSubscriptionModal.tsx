'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import type { PmaSubscriptionWithAccess } from '@/types/billing';
import { getNext27thDate } from '@/types/billing';
import { AlertTriangle, Calendar, Shield } from 'lucide-react';
import { useState } from 'react';

export interface CancelSubscriptionModalProps {
  subscription: PmaSubscriptionWithAccess;
  projectName?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirmCancel?: (subscriptionId: string) => void;
  isLoading?: boolean;
}

export function CancelSubscriptionModal({
  subscription,
  projectName,
  open,
  onOpenChange,
  onConfirmCancel,
  isLoading = false,
}: CancelSubscriptionModalProps) {
  const [isConfirming, setIsConfirming] = useState(false);

  const next27th = getNext27thDate();
  const _monthlyAmount = subscription.amount || 0;
  const isImmediateSuspension =
    subscription.status === 'grace_period' || subscription.status === 'trial';

  const handleConfirm = async () => {
    if (!onConfirmCancel) return;

    setIsConfirming(true);
    try {
      await onConfirmCancel(subscription.id);
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
    } finally {
      setIsConfirming(false);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-MY', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Cancel Subscription
          </DialogTitle>
          <DialogDescription className="mt-1">
            {projectName || 'Unnamed Project'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning Message */}
          <div
            className={`p-4 border rounded-lg ${
              isImmediateSuspension
                ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                : 'bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800'
            }`}
          >
            <div className="flex gap-3">
              <Shield
                className={`h-5 w-5 mt-0.5 flex-shrink-0 ${
                  isImmediateSuspension ? 'text-red-600' : 'text-amber-600'
                }`}
              />
              <div className="space-y-2">
                {isImmediateSuspension ? (
                  <>
                    <h4 className="font-medium text-red-800 dark:text-red-200">
                      Access will be suspended immediately
                    </h4>
                    <p className="text-sm text-red-700 dark:text-red-300">
                      {subscription.status === 'trial'
                        ? 'Since you are currently on a trial subscription, cancelling will immediately suspend your access to the PMA system.'
                        : 'Since your subscription is currently in a grace period due to unpaid bills, cancelling will immediately suspend your access to the PMA system.'}
                    </p>
                  </>
                ) : (
                  <>
                    <h4 className="font-medium text-amber-800 dark:text-amber-200">
                      Your access will continue until {formatDate(next27th)}
                    </h4>
                    <p className="text-sm text-amber-700 dark:text-amber-300">
                      After cancellation, you&apos;ll retain full access to your
                      PMA until the 27th. On the 27th of each month, all
                      cancelled subscriptions are automatically suspended.
                    </p>
                  </>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Subscription Details */}
          <div className="space-y-3">
            <h3 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
              Subscription Details
            </h3>

            <div className="grid grid-cols-2 gap-4 text-sm">
              {/* Monthly amount hidden as per requirements */}
              {/* <div className="space-y-1">
                <p className="text-muted-foreground">Monthly Amount</p>
                <p className="font-semibold">RM {monthlyAmount.toFixed(2)}</p>
              </div> */}

              <div className="space-y-1">
                <p className="text-muted-foreground">Access Until</p>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3 text-muted-foreground" />
                  <p className="font-semibold">
                    {isImmediateSuspension
                      ? 'Immediately suspended'
                      : formatDate(next27th)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* What Happens Next */}
          <div className="space-y-3">
            <h3 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
              What Happens Next
            </h3>

            <div className="space-y-2 text-sm">
              {isImmediateSuspension ? (
                <>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                    <p>Your subscription will be immediately suspended</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                    <p>Access to the PMA system will be blocked immediately</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <p>
                      No additional charges will be made to your payment method
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <p>You can reactivate your subscription at any time</p>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <p>
                      Your subscription status will change to
                      &quot;Cancelled&quot;
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <p>
                      You&apos;ll continue to have full access until{' '}
                      {formatDate(next27th)}
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <p>
                      No additional charges will be made to your payment method
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <p>
                      On the 27th, your subscription will be automatically
                      suspended
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <p>
                      You can reactivate your subscription at any time before
                      the 27th
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2 pt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isConfirming || isLoading}
          >
            Keep Subscription
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isConfirming || isLoading}
          >
            {isConfirming || isLoading
              ? 'Cancelling...'
              : 'Cancel Subscription'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
