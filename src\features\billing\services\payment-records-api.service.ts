import { supabase } from '@/lib/supabase';
import type { <PERSON><PERSON> } from '@/types/database';
import type {
  PaymentRecordsQueryParams,
  PaymentRecordsResponse,
  PaymentRecordWithSubscription,
  BatchedPayment,
  BatchedPaymentWithRecords,
  BatchedPaymentsQueryParams,
  BatchedPaymentsResponse,
  CreateBatchedPaymentRequest,
} from '../types/payment-records';

/**
 * Fetch payment records with filtering and pagination
 */
export async function getPaymentRecords(
  params: PaymentRecordsQueryParams,
): Promise<PaymentRecordsResponse> {
  const {
    contractorId,
    status,
    dateFrom,
    dateTo,
    subscriptionId,
    batchedPaymentId,
    page = 1,
    limit = 50,
  } = params;

  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  // Build the optimized query - only select needed fields
  let query = supabase
    .from('pma_payment_records')
    .select(
      `
      id,
      pma_subscription_id,
      billplz_bill_id,
      amount,
      status,
      paid_at,
      failure_reason,
      created_at,
      pma_subscription:pma_subscriptions!inner(
        id,
        contractor_id,
        pma_certificates!inner(
          id,
          pma_number,
          projects!inner(
            id,
            name
          )
        )
      )
    `,
      { count: 'exact' },
    )
    .eq('pma_subscription.contractor_id', contractorId);

  // Apply filters early for better performance
  if (status) {
    query = query.eq('status', status);
  }

  if (dateFrom) {
    query = query.gte('created_at', dateFrom);
  }

  if (dateTo) {
    query = query.lte('created_at', dateTo);
  }

  if (subscriptionId) {
    query = query.eq('pma_subscription_id', subscriptionId);
  }

  if (batchedPaymentId) {
    query = query.eq('batched_payment_id', batchedPaymentId);
  }

  // Apply ordering and pagination
  query = query
    .order('created_at', { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch payment records: ${error.message}`);
  }

  return {
    records: (data as unknown as PaymentRecordWithSubscription[]) || [],
    total: count || 0,
    hasMore: (data?.length || 0) === limit,
  };
}

/**
 * Fetch a single payment record by ID
 */
export async function getPaymentRecordById(
  id: string,
): Promise<PaymentRecordWithSubscription | null> {
  if (!id) {
    throw new Error('Payment record ID is required');
  }

  const { data, error } = await supabase
    .from('pma_payment_records')
    .select(
      `
      id,
      pma_subscription_id,
      billplz_bill_id,
      amount,
      status,
      paid_at,
      failure_reason,
      billplz_response,
      created_at,
      pma_subscription:pma_subscriptions!inner(
        id,
        contractor_id,
        pma_certificates!inner(
          id,
          pma_number,
          projects!inner(
            id,
            name
          )
        )
      )
    `,
    )
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(`Failed to fetch payment record: ${error.message}`);
  }

  return data as unknown as PaymentRecordWithSubscription;
}

/**
 * Get payment summary statistics for a contractor
 */
export async function getPaymentSummary(contractorId: string) {
  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  const { data, error } = await supabase
    .from('pma_payment_records')
    .select(
      `
      status,
      amount,
      pma_subscription:pma_subscriptions!inner(
        contractor_id
      )
    `,
    )
    .eq('pma_subscription.contractor_id', contractorId);

  if (error) {
    throw new Error(`Failed to fetch payment summary: ${error.message}`);
  }

  const summary = {
    total: data?.length || 0,
    totalAmount: data?.reduce((sum, record) => sum + record.amount, 0) || 0,
    paid: data?.filter((record) => record.status === 'paid').length || 0,
    pending: data?.filter((record) => record.status === 'pending').length || 0,
    failed: data?.filter((record) => record.status === 'failed').length || 0,
    paidAmount:
      data
        ?.filter((record) => record.status === 'paid')
        .reduce((sum, record) => sum + record.amount, 0) || 0,
  };

  return summary;
}

/**
 * Get payment record data specifically for receipt generation
 * Only returns data for paid payments that can have receipts
 */
export async function getPaymentReceiptData(
  paymentId: string,
): Promise<PaymentRecordWithSubscription> {
  if (!paymentId) {
    throw new Error('Payment ID is required');
  }

  const paymentRecord = await getPaymentRecordById(paymentId);

  if (!paymentRecord) {
    throw new Error('Payment record not found');
  }

  if (paymentRecord.status !== 'paid') {
    throw new Error('Receipt not available for unpaid payments');
  }

  return paymentRecord;
}

/**
 * Get payment record data specifically for invoice generation
 * Works for all payment records regardless of status, uses created_at date
 */
export async function getPaymentInvoiceData(
  paymentId: string,
): Promise<PaymentRecordWithSubscription> {
  if (!paymentId) {
    throw new Error('Payment ID is required');
  }

  const paymentRecord = await getPaymentRecordById(paymentId);

  if (!paymentRecord) {
    throw new Error('Payment record not found');
  }

  return paymentRecord;
}

// ================================
// BATCHED PAYMENTS API FUNCTIONS
// ================================

/**
 * Fetch batched payments with filtering and pagination
 */
export async function getBatchedPayments(
  params: BatchedPaymentsQueryParams,
): Promise<BatchedPaymentsResponse> {
  const {
    contractorId,
    status,
    dateFrom,
    dateTo,
    pmaCount,
    page = 1,
    limit = 50,
  } = params;

  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  // Build the query for batched payments
  let query = supabase
    .from('pma_batched_payments')
    .select('*', { count: 'exact' })
    .eq('contractor_id', contractorId);

  // Apply filters
  if (status) {
    query = query.eq('status', status);
  }

  if (dateFrom) {
    query = query.gte('created_at', dateFrom);
  }

  if (dateTo) {
    query = query.lte('created_at', dateTo);
  }

  if (pmaCount) {
    query = query.eq('total_pma_count', pmaCount);
  }

  // Apply ordering and pagination
  query = query
    .order('created_at', { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch batched payments: ${error.message}`);
  }

  return {
    batches: (data as BatchedPayment[]) || [],
    total: count || 0,
    hasMore: (data?.length || 0) === limit,
  };
}

/**
 * Fetch a single batched payment by ID
 */
export async function getBatchedPaymentById(
  id: string,
): Promise<BatchedPayment | null> {
  if (!id) {
    throw new Error('Batched payment ID is required');
  }

  const { data, error } = await supabase
    .from('pma_batched_payments')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(`Failed to fetch batched payment: ${error.message}`);
  }

  return data as BatchedPayment;
}

/**
 * Fetch a batched payment with its associated payment records
 */
export async function getBatchedPaymentWithRecords(
  id: string,
): Promise<BatchedPaymentWithRecords | null> {
  if (!id) {
    throw new Error('Batched payment ID is required');
  }

  // First get the batched payment
  const batchedPayment = await getBatchedPaymentById(id);
  if (!batchedPayment) {
    return null;
  }

  // Then get the associated payment records
  const { data: paymentRecords, error } = await supabase
    .from('pma_payment_records')
    .select(
      `
      id,
      pma_subscription_id,
      billplz_bill_id,
      amount,
      status,
      paid_at,
      failure_reason,
      created_at,
      batched_payment_id,
      pma_subscription:pma_subscriptions!inner(
        id,
        contractor_id,
        pma_certificates!inner(
          id,
          pma_number,
          projects!inner(
            id,
            name
          )
        )
      )
    `,
    )
    .eq('batched_payment_id', id);

  if (error) {
    throw new Error(
      `Failed to fetch payment records for batch: ${error.message}`,
    );
  }

  return {
    ...batchedPayment,
    payment_records:
      (paymentRecords as unknown as PaymentRecordWithSubscription[]) || [],
  };
}

/**
 * Create a new batched payment record
 */
export async function createBatchedPayment(
  request: CreateBatchedPaymentRequest,
): Promise<BatchedPayment> {
  const {
    contractor_id,
    subscription_ids,
    total_amount,
    pma_numbers,
    batch_bill_id,
  } = request;

  if (!contractor_id) {
    throw new Error('Contractor ID is required');
  }

  if (!subscription_ids.length) {
    throw new Error('Subscription IDs are required');
  }

  if (subscription_ids.length < 1) {
    throw new Error('Batched payments require at least 1 PMA');
  }

  if (pma_numbers.length !== subscription_ids.length) {
    throw new Error('PMA numbers must match subscription count');
  }

  // Fetch subscription amounts to calculate total_base_amount
  const { data: subscriptions, error: subscriptionError } = await supabase
    .from('pma_subscriptions')
    .select('amount')
    .in('id', subscription_ids);

  if (subscriptionError) {
    throw new Error(
      `Failed to fetch subscription amounts: ${subscriptionError.message}`,
    );
  }

  if (!subscriptions || subscriptions.length !== subscription_ids.length) {
    throw new Error('Some subscriptions not found');
  }

  // Calculate total_base_amount by summing up all subscription amounts
  const total_base_amount = subscriptions.reduce(
    (sum, sub) => sum + Number(sub.amount),
    0,
  );

  const batchData = {
    contractor_id,
    total_amount,
    total_base_amount,
    total_pma_count: subscription_ids.length,
    pma_numbers,
    batch_bill_id,
    status: 'pending' as const,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const { data: batchedPayment, error } = await supabase
    .from('pma_batched_payments')
    .insert(batchData)
    .select('*')
    .single();

  if (error || !batchedPayment) {
    throw new Error(
      `Failed to create batched payment: ${error?.message || 'No data returned'}`,
    );
  }

  return batchedPayment as BatchedPayment;
}

/**
 * Update batched payment status and related fields
 */
export async function updateBatchedPaymentStatus(
  batchId: string,
  status: 'paid' | 'failed' | 'cancelled',
  updateData?: {
    paid_at?: string;
    failure_reason?: string;
    billplz_response?: Json;
  },
): Promise<BatchedPayment> {
  if (!batchId) {
    throw new Error('Batch ID is required');
  }

  const updatePayload = {
    status,
    updated_at: new Date().toISOString(),
    ...updateData,
  };

  const { data: updatedBatch, error } = await supabase
    .from('pma_batched_payments')
    .update(updatePayload)
    .eq('id', batchId)
    .select('*')
    .single();

  if (error || !updatedBatch) {
    throw new Error(
      `Failed to update batched payment: ${error?.message || 'No data returned'}`,
    );
  }

  return updatedBatch as BatchedPayment;
}

/**
 * Get batched payment by batch bill ID (for webhook processing)
 */
export async function getBatchedPaymentByBillId(
  batchBillId: string,
): Promise<BatchedPayment | null> {
  if (!batchBillId) {
    throw new Error('Batch bill ID is required');
  }

  const { data, error } = await supabase
    .from('pma_batched_payments')
    .select('*')
    .eq('batch_bill_id', batchBillId)
    .single();

  if (error) {
    console.error('Failed to fetch batched payment by bill ID:', error);
    return null;
  }

  return data as BatchedPayment;
}

/**
 * Get batched payment summary statistics for a contractor
 */
export async function getBatchedPaymentSummary(contractorId: string) {
  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  const { data, error } = await supabase
    .from('pma_batched_payments')
    .select('status, total_amount')
    .eq('contractor_id', contractorId);

  if (error) {
    throw new Error(
      `Failed to fetch batched payment summary: ${error.message}`,
    );
  }

  const summary = {
    total: data?.length || 0,
    totalAmount: data?.reduce((sum, batch) => sum + batch.total_amount, 0) || 0,
    paid: data?.filter((batch) => batch.status === 'paid').length || 0,
    pending: data?.filter((batch) => batch.status === 'pending').length || 0,
    failed: data?.filter((batch) => batch.status === 'failed').length || 0,
    paidAmount:
      data
        ?.filter((batch) => batch.status === 'paid')
        .reduce((sum, batch) => sum + batch.total_amount, 0) || 0,
  };

  return summary;
}

// Legacy class for backward compatibility (can be removed after refactoring)
export class PaymentRecordsApiService {
  async getPaymentRecords(
    params: PaymentRecordsQueryParams,
  ): Promise<PaymentRecordsResponse> {
    return getPaymentRecords(params);
  }

  async getPaymentRecordById(
    id: string,
  ): Promise<PaymentRecordWithSubscription | null> {
    return getPaymentRecordById(id);
  }

  async getPaymentSummary(contractorId: string) {
    return getPaymentSummary(contractorId);
  }

  // Batched payments methods
  async getBatchedPayments(
    params: BatchedPaymentsQueryParams,
  ): Promise<BatchedPaymentsResponse> {
    return getBatchedPayments(params);
  }

  async getBatchedPaymentById(id: string): Promise<BatchedPayment | null> {
    return getBatchedPaymentById(id);
  }

  async getBatchedPaymentWithRecords(
    id: string,
  ): Promise<BatchedPaymentWithRecords | null> {
    return getBatchedPaymentWithRecords(id);
  }

  async createBatchedPayment(
    request: CreateBatchedPaymentRequest,
  ): Promise<BatchedPayment> {
    return createBatchedPayment(request);
  }
}
