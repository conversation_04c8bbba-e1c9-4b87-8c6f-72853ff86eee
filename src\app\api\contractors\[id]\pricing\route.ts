import { ContractorPackageService } from '@/features/billing/services/contractor-package.service';
import { authenticateWithPermission } from '@/features/auth';
import { NextRequest, NextResponse } from 'next/server';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/contractors/[id]/pricing
 * Get the current pricing for a contractor based on their package
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id: contractorId } = await params;

    // Check if user can access this contractor's data
    const isAdmin = user.user_role === 'admin';
    const isOwnContractor = user.contractor_id === contractorId;

    if (!isAdmin && !isOwnContractor) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const result =
      await ContractorPackageService.getContractorPricing(contractorId);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({
      contractor_id: contractorId,
      pricing: result.data,
      currency: 'MYR',
      description: 'Monthly subscription pricing based on contractor package',
    });
  } catch (error) {
    console.error('Get contractor pricing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
