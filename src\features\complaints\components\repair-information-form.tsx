'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUploadWithPreview } from '@/components/ui/file-upload-with-preview';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PhotoViewer } from '@/components/ui/photo-viewer';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { uploadToOBS } from '@/lib/obs-upload';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useUpdateComplaint } from '../hooks/use-complaints-simple';
import {
  CreateComplaintInput,
  UpdateRepairInformationInput,
  updateRepairInformationSchema,
} from '../schemas';

interface RepairInformationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  complaintId: string;
  initialData?: Partial<CreateComplaintInput>;
}

export function RepairInformationForm({
  onSuccess,
  onCancel,
  complaintId,
  initialData,
}: RepairInformationFormProps) {
  const [proofOfRepairFiles, setProofOfRepairFiles] = useState<File[]>([]);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const updateComplaintMutation = useUpdateComplaint();
  const t = useTranslations('complaints.form');

  // Helper to uniquely identify a File instance
  const fileKey = (f: File) => `${f.name}-${f.size}-${f.lastModified}`;

  const form = useForm<UpdateRepairInformationInput>({
    resolver: zodResolver(updateRepairInformationSchema),
    defaultValues: {
      // Section A: Basic Information (optional for updates)
      email: initialData?.email,
      date: initialData?.date,
      expected_completion_date: initialData?.expected_completion_date,
      contractor_name: initialData?.contractor_name,
      location: initialData?.location,
      no_pma_lif: initialData?.no_pma_lif,
      description: initialData?.description,
      involves_mantrap: initialData?.involves_mantrap || false,

      // Section B: Repair Information - preserve existing data for editing
      actual_completion_date: initialData?.actual_completion_date || new Date(),
      repair_completion_time: initialData?.repair_completion_time || '',
      cause_of_damage: initialData?.cause_of_damage || '',
      correction_action: initialData?.correction_action || '',
      repair_cost: initialData?.repair_cost || 0,
      proof_of_repair_urls: initialData?.proof_of_repair_urls || [],

      // Preserve current status - don't automatically change to closed
      status:
        (initialData?.status as 'open' | 'on_hold' | 'closed') || 'closed',
    },
  });

  const handleFilesChange = async (files: File[]) => {
    // Just update the state without uploading immediately
    // This allows users to preview files before form submission
    const existingKeys = new Set(proofOfRepairFiles.map(fileKey));
    const newlyAdded = files.filter((f) => !existingKeys.has(fileKey(f)));

    // Merge without duplicates for stable rendering in the dropzone
    const deduped = [
      ...proofOfRepairFiles,
      ...newlyAdded.filter(
        (f, idx, arr) =>
          arr.findIndex((x) => fileKey(x) === fileKey(f)) === idx,
      ),
    ];

    setProofOfRepairFiles(deduped);

    // Note: Upload will happen during form submission, not immediately
    // This prevents automatic form submission when files are selected
  };

  const uploadFiles = async (files: File[]): Promise<string[]> => {
    const newUrls: string[] = [];

    try {
      for (const file of files) {
        const url = await uploadToOBS({
          file,
          folder: 'complaints/proof-of-repair',
        });
        newUrls.push(url);
      }

      // Update the uploaded URLs state for potential re-renders
      setUploadedUrls(newUrls);
      return newUrls;
    } catch (error) {
      console.error('Failed to upload files:', error);
      throw error; // Re-throw to handle in submission
    }
  };

  const removeFile = (index: number) => {
    setProofOfRepairFiles((prev) => prev.filter((_, i) => i !== index));
    setUploadedUrls((prev) => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: UpdateRepairInformationInput) => {
    try {
      console.log('Form submitted with data:', data);
      console.log('Initial data:', initialData);
      console.log('Proof of repair files:', proofOfRepairFiles);

      // Upload files first if any are selected
      let newUploadedUrls: string[] = [];

      if (proofOfRepairFiles.length > 0) {
        setIsUploading(true);
        newUploadedUrls = await uploadFiles(proofOfRepairFiles);
      }

      // Preserve existing data and allow multiple edits
      const updatedData: CreateComplaintInput = {
        // Include existing Section A data from initialData
        email: initialData?.email || '',
        date: initialData?.date || new Date(),
        expected_completion_date:
          initialData?.expected_completion_date || new Date(),
        contractor_name: initialData?.contractor_name || '',
        location: initialData?.location || '',
        no_pma_lif: initialData?.no_pma_lif || '',
        description: initialData?.description || '',
        involves_mantrap: initialData?.involves_mantrap || false,

        // Section B data from form - updated values
        actual_completion_date: data.actual_completion_date,
        repair_completion_time: data.repair_completion_time,
        cause_of_damage: data.cause_of_damage,
        correction_action: data.correction_action,
        // Merge existing proof URLs with newly uploaded URLs
        proof_of_repair_urls: [
          ...(initialData?.proof_of_repair_urls || []),
          ...newUploadedUrls,
        ],
        repair_cost: data.repair_cost,

        // Preserve current status or set to closed if it's the first time completing Section B
        status:
          initialData?.status === 'open' || initialData?.status === 'on_hold'
            ? 'closed'
            : initialData?.status || 'closed',
      };

      console.log('Sending update with data:', updatedData);

      await updateComplaintMutation.mutateAsync({
        id: complaintId,
        data: updatedData,
      });

      console.log('Update successful!');
      onSuccess();
    } catch (error) {
      console.error('Failed to update complaint:', error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6 max-w-5xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('title')} - {t('sectionB.title')}
        </h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <form
        onSubmit={form.handleSubmit(onSubmit, (errors) => {
          console.log('Form validation errors:', errors);
          console.log('Form values:', form.getValues());
          console.log('Form state:', form.formState);
        })}
        className="space-y-6"
      >
        {/* Section B: Repair Information */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4 bg-green-50">
            <CardTitle className="text-lg font-semibold text-green-800 flex items-center gap-2">
              {t('sectionB.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6">
            {/* Actual Completion Date */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionB.actualCompletionDate')}
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal h-10',
                      !form.watch('actual_completion_date') &&
                        'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('actual_completion_date') ? (
                      format(
                        form.watch('actual_completion_date') as Date,
                        'yyyy-MM-dd',
                      )
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={form.watch('actual_completion_date') as Date}
                    onSelect={(date) =>
                      form.setValue(
                        'actual_completion_date',
                        date || new Date(),
                      )
                    }
                  />
                </PopoverContent>
              </Popover>
              {form.formState.errors.actual_completion_date && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.actual_completion_date.message}
                </p>
              )}
            </div>

            {/* Repair Completion Time */}
            <div className="space-y-2">
              <Label
                htmlFor="repair_completion_time"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionB.repairCompletionTime')}
              </Label>
              <Input
                id="repair_completion_time"
                type="time"
                {...form.register('repair_completion_time')}
                className="h-10"
              />
              {form.formState.errors.repair_completion_time && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.repair_completion_time.message}
                </p>
              )}
            </div>

            {/* Cause of Damage */}
            <div className="space-y-2">
              <Label
                htmlFor="cause_of_damage"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionB.causeOfDamage')}
              </Label>
              <Textarea
                id="cause_of_damage"
                {...form.register('cause_of_damage')}
                placeholder="Describe the cause of damage"
                className="min-h-[80px]"
              />
              {form.formState.errors.cause_of_damage && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.cause_of_damage.message}
                </p>
              )}
            </div>

            {/* Correction Action */}
            <div className="space-y-2">
              <Label
                htmlFor="correction_action"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionB.correctionAction')}
              </Label>
              <Textarea
                id="correction_action"
                {...form.register('correction_action')}
                placeholder="Describe the correction action taken"
                className="min-h-[80px]"
              />
              {form.formState.errors.correction_action && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.correction_action.message}
                </p>
              )}
            </div>

            {/* Repair Cost */}
            <div className="space-y-2">
              <Label
                htmlFor="repair_cost"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionB.repairCost')} (RM)
              </Label>
              <Input
                id="repair_cost"
                type="number"
                step="0.01"
                min="0"
                {...form.register('repair_cost', { valueAsNumber: true })}
                className="h-10"
                placeholder="0.00"
              />
              {form.formState.errors.repair_cost && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.repair_cost.message}
                </p>
              )}
            </div>

            {/* Proof of Repair Files */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionB.proofOfRepair')}
              </Label>

              {/* Display existing proof of repair URLs only if Section B is completed */}
              {initialData?.proof_of_repair_urls &&
                initialData.proof_of_repair_urls.length > 0 &&
                initialData?.status === 'closed' &&
                initialData?.actual_completion_date &&
                initialData?.repair_completion_time &&
                initialData?.cause_of_damage &&
                initialData?.correction_action && (
                  <div className="mb-3">
                    <PhotoViewer
                      photos={initialData.proof_of_repair_urls.map(
                        (url, index) => ({
                          url,
                          name:
                            url.split('/').pop() ||
                            `Previous Proof File ${index + 1}`,
                        }),
                      )}
                      title="Previously Uploaded Files"
                    />
                  </div>
                )}

              <FileUploadWithPreview
                onFilesChange={handleFilesChange}
                accept=".jpg,.jpeg,.png,.pdf"
                maxSize={10 * 1024 * 1024} // 10MB
                maxFiles={5}
                files={proofOfRepairFiles}
                onFileRemove={removeFile}
                disabled={isUploading}
                allowPreview={true}
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                Upload photos or documents showing the completed repair (JPG,
                PNG, PDF - max 10MB each)
              </p>

              {/* Display upload status */}
              {isUploading && (
                <div className="text-sm text-blue-600 flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
                  Uploading files during submission...
                </div>
              )}

              {/* Show preview message */}
              {proofOfRepairFiles.length > 0 &&
                !isUploading &&
                uploadedUrls.length === 0 && (
                  <div className="text-sm text-green-600 flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                    <span>📸</span>
                    <span>
                      {proofOfRepairFiles.length} file(s) selected. Files will
                      be uploaded when you submit the form.
                    </span>
                  </div>
                )}

              {/* Display newly uploaded files with PhotoViewer - Only show after successful upload */}
              {uploadedUrls.length > 0 && !isUploading && (
                <div className="mt-4">
                  <PhotoViewer
                    photos={uploadedUrls.map((url, index) => ({
                      url,
                      name:
                        url.split('/').pop() || `Proof of Repair ${index + 1}`,
                    }))}
                    title="Successfully Uploaded Files"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="px-6"
          >
            {t('actions.cancel')}
          </Button>
          <Button
            type="submit"
            className="px-6"
            disabled={updateComplaintMutation.isPending || isUploading}
            onClick={() => {
              console.log('Submit button clicked');
              console.log('Form errors:', form.formState.errors);
              console.log('Form values:', form.getValues());
              console.log('Form is valid:', form.formState.isValid);
            }}
          >
            {updateComplaintMutation.isPending || isUploading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {isUploading ? 'Uploading files...' : 'Updating...'}
              </>
            ) : (
              t('actions.updateSectionB')
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
