import { ContractorPackageService } from '@/features/billing/services/contractor-package.service';
import { authenticateWithPermission } from '@/features/auth';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

const AssignPackageSchema = z.object({
  package_id: z.string().uuid('Invalid package ID'),
});

/**
 * GET /api/contractors/[id]/package
 * Get the package assigned to a contractor
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id: contractorId } = await params;

    // Check if user can access this contractor's data
    const isAdmin = user.user_role === 'admin';
    const isOwnContractor = user.contractor_id === contractorId;

    if (!isAdmin && !isOwnContractor) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const result =
      await ContractorPackageService.getContractorPackage(contractorId);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ package: result.data });
  } catch (error) {
    console.error('Get contractor package error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * PATCH /api/contractors/[id]/package
 * Assign or remove a package from a contractor
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await authenticateWithPermission('projects.create');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id: contractorId } = await params;

    // Check if user can modify this contractor's data
    const isAdmin = user.user_role === 'admin';
    const isOwnContractor = user.contractor_id === contractorId;

    if (!isAdmin && !isOwnContractor) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();

    // Handle package removal
    if (body.package_id === null) {
      const result =
        await ContractorPackageService.removeFromContractor(contractorId);

      if (!result.success) {
        return NextResponse.json({ error: result.error }, { status: 500 });
      }

      return NextResponse.json({
        message: 'Package removed from contractor successfully',
      });
    }

    // Handle package assignment
    const validation = AssignPackageSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { package_id } = validation.data;
    const result = await ContractorPackageService.assignToContractor(
      contractorId,
      package_id,
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error?.includes('not found') ? 404 : 500 },
      );
    }

    return NextResponse.json({
      message: 'Package assigned to contractor successfully',
    });
  } catch (error) {
    console.error('Update contractor package error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
