import { useQuery } from '@tanstack/react-query';
import { getPaymentReceiptData } from '../services/payment-records-api.service';
import type { PaymentRecordWithSubscription } from '../types/payment-records';

interface UsePaymentReceiptParams {
  paymentId?: string;
  enabled?: boolean;
}

/**
 * Hook for fetching payment receipt data
 * Only works for paid payments and provides optimized caching for receipts
 */
export function usePaymentReceipt({
  paymentId,
  enabled = true,
}: UsePaymentReceiptParams) {
  return useQuery({
    queryKey: ['payment-receipt', paymentId],
    queryFn: (): Promise<PaymentRecordWithSubscription> => {
      return getPaymentReceiptData(paymentId!);
    },
    enabled: enabled && !!paymentId,
    staleTime: 30 * 60 * 1000, // 30 minutes - receipts don't change frequently
    gcTime: 60 * 60 * 1000, // 1 hour
  });
}
