'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  ComplaintFilterSection,
  ComplaintFilters,
} from '@/features/complaints/components';
import {
  ComplaintWithRelations,
  useComplaints,
} from '@/features/complaints/hooks/use-complaints-simple';
import { ComplaintUI } from '@/features/complaints/types/ui-types';
import { usePermissions } from '@/hooks/use-permissions';
import { useProjectContext } from '@/providers/project-context';
import {
  AlertCircle,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Edit,
  Eye,
  Plus,
  TrendingUp,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Define filter types - simplified to only RegularFilters
type RegularFilters = {
  search: string;
  status: string | undefined;
  followUp: string | undefined;
  dateRange: { from?: Date; to?: Date };
};

type UnifiedFilters = RegularFilters;

// Type guard functions - simplified since we only use RegularFilters now
const isRegularFilters = (
  filters: UnifiedFilters,
): filters is RegularFilters => {
  return true; // Always true since we only have RegularFilters
};

// Helper functions to convert between filter types - simplified
const convertToComplaintFilters = (
  filters: UnifiedFilters,
): ComplaintFilters => {
  return {
    search: filters.search,
    status: filters.status,
    followUp: filters.followUp,
    dateRange: filters.dateRange,
  };
};

const convertFromComplaintFilters = (
  complaintFilters: ComplaintFilters,
  currentFilters: UnifiedFilters,
): UnifiedFilters => {
  return {
    ...currentFilters,
    search: complaintFilters.search || '',
    status: complaintFilters.status,
    followUp: complaintFilters.followUp,
    dateRange: complaintFilters.dateRange || {
      from: undefined,
      to: undefined,
    },
  };
};

export default function ComplaintsPage() {
  const router = useRouter();
  const { selectedProjectId } = useProjectContext();
  const { userRole } = usePermissions();

  // Translations
  const t = useTranslations('complaints');

  // Filter states - unified type for both admin and contractor (same filters)
  const [filters, setFilters] = useState<UnifiedFilters>({
    search: '',
    status: undefined,
    followUp: undefined,
    dateRange: { from: undefined, to: undefined },
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Remove the project sync effect since both admin and contractor use project-filtered data

  // Use the same hook for both admin and contractor - both should see project-specific complaints
  const { data: complaintsData = [], isLoading, error } = useComplaints(); // Both admin and contractor use the same project-filtered hook
  // Simplified filtering logic - same for both admin and contractor
  const getFilteredComplaints = () => {
    if (!complaintsData) return [];

    let filtered = complaintsData;

    if (isRegularFilters(filters)) {
      // Regular user filtering logic (used by both contractor and admin)
      filtered = filtered.filter((complaint) => {
        // Search filter
        const matchesSearch =
          !filters.search ||
          complaint.email
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          complaint.contractor_name
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          complaint.location
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          complaint.no_pma_lif
            ?.toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          complaint.description
            ?.toLowerCase()
            .includes(filters.search.toLowerCase());

        // Status filter
        const matchesStatus =
          !filters.status || complaint.status === filters.status;

        // Follow up filter
        const matchesFollowUp =
          !filters.followUp || complaint.follow_up === filters.followUp;

        // Date range filter
        const matchesDateRange = (() => {
          if (!filters.dateRange.from) return true;
          const complaintDate = new Date(complaint.date);
          const fromDate = filters.dateRange.from;
          const toDate = filters.dateRange.to || fromDate;
          return complaintDate >= fromDate && complaintDate <= toDate;
        })();

        return (
          matchesSearch && matchesStatus && matchesFollowUp && matchesDateRange
        );
      });
    }

    return filtered;
  };

  // Handle filter changes - unified for both admin and contractor
  const handleFilterChange = (newFilters: UnifiedFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Wrapper function for ComplaintFilterSection - used by both admin and contractor
  const handleRegularUserFilterChange = (
    complaintFilters: ComplaintFilters,
  ) => {
    const newUnifiedFilters = convertFromComplaintFilters(
      complaintFilters,
      filters,
    );
    handleFilterChange(newUnifiedFilters);
  };

  // Helper function to determine if Section A is completed
  const isSectionACompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.email &&
      complaint.date &&
      complaint.expected_completion_date &&
      complaint.contractor_name &&
      complaint.location &&
      complaint.no_pma_lif &&
      complaint.description
    );
  };

  // Helper function to determine if Section B is completed
  const isSectionBCompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.actual_completion_date &&
      complaint.repair_completion_time &&
      complaint.cause_of_damage &&
      complaint.correction_action &&
      complaint.proof_of_repair_urls &&
      complaint.proof_of_repair_urls.length > 0
    );
  };

  // Helper function to determine follow-up status based on completion and admin approval
  /**
   * Determines follow-up status based on section completion and admin verification:
   * 1. 'in_progress': User completed Section A (damage complaint form) only
   * 2. 'pending_approval': User completed both Section A & B (damage complaint + repair info)
   * 3. 'verified': Admin has approved the complaint (only admin can set this)
   */
  const determineFollowUpStatus = (
    complaint: ComplaintWithRelations,
  ): 'in_progress' | 'pending_approval' | 'verified' => {
    const sectionAComplete = isSectionACompleted(complaint);
    const sectionBComplete = isSectionBCompleted(complaint);

    // If admin has verified/approved, return verified (only admin can set this)
    if (complaint.follow_up === 'verified') {
      return 'verified';
    }

    // If both sections are completed, set to pending approval
    if (sectionAComplete && sectionBComplete) {
      return 'pending_approval';
    }

    // If only Section A is completed, set to in progress
    if (sectionAComplete) {
      return 'in_progress';
    }

    // If neither section is completed, default to in_progress
    return 'in_progress';
  };

  // Convert database complaints to UI format for both admin and contractor
  const rawComplaints: ComplaintUI[] = complaintsData.map((complaint) => {
    const sectionAComplete = isSectionACompleted(complaint);
    const sectionBComplete = isSectionBCompleted(complaint);
    const computedFollowUp = determineFollowUpStatus(complaint);

    return {
      // Direct database field mapping
      id: complaint.id,
      email: complaint.email,
      number:
        complaint.number ||
        `DCL-${new Date(complaint.created_at || '').getFullYear()}-${complaint.id.slice(-4)}`,
      date: complaint.date || complaint.created_at || new Date().toISOString(),
      expected_completion_date:
        complaint.expected_completion_date ||
        complaint.created_at ||
        new Date().toISOString(),
      contractor_name: complaint.contractor_name,
      location: complaint.location,
      no_pma_lif: complaint.no_pma_lif,
      description: complaint.description,
      involves_mantrap: complaint.involves_mantrap,

      // Section B fields
      actual_completion_date: complaint.actual_completion_date,
      repair_completion_time: complaint.repair_completion_time,
      cause_of_damage: complaint.cause_of_damage,
      correction_action: complaint.correction_action,
      proof_of_repair_urls: complaint.proof_of_repair_urls,
      before_repair_files: complaint.before_repair_files,
      repair_cost: complaint.repair_cost,

      // Status and metadata
      status: complaint.status,
      follow_up: computedFollowUp, // Use computed follow-up status
      created_at: complaint.created_at,

      // Computed fields
      sectionACompleted: sectionAComplete,
      sectionBCompleted: sectionBComplete,
      proofOfRepairFiles:
        complaint.proof_of_repair_urls?.map((url: string, index: number) => ({
          name: `Repair Evidence ${index + 1}`,
          url,
          size: 0, // Size not stored in current schema
        })) || [],
      beforeRepairFiles:
        complaint.before_repair_files?.map((url: string, index: number) => ({
          name: `Before Repair Photo ${index + 1}`,
          url,
          size: 0, // Size not stored in current schema
        })) || [],
    };
  });

  // Apply filters to get final complaints list - same for both admin and contractor
  const filteredComplaints = getFilteredComplaints();

  // Pagination calculations
  const totalPages = Math.ceil(filteredComplaints.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedComplaints = filteredComplaints.slice(startIndex, endIndex);

  // Pagination handlers
  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handlePageClick = (page: number) => {
    setCurrentPage(page);
  };

  // Stats calculations - same for both admin and contractor
  const baseStatsData = rawComplaints;

  // Calculate stats - same for both admin and contractor
  const getStats = () => {
    const inProgressReports = rawComplaints.filter(
      (c) => c.follow_up === 'in_progress',
    ).length;
    const pendingApprovalReports = rawComplaints.filter(
      (c) => c.follow_up === 'pending_approval',
    ).length;
    const verifiedReports = rawComplaints.filter(
      (c) => c.follow_up === 'verified',
    ).length;
    const totalReports = rawComplaints.length;

    return {
      totalReports,
      inProgressReports,
      pendingApprovalReports,
      verifiedReports,
    };
  };

  const {
    totalReports,
    inProgressReports,
    pendingApprovalReports,
    verifiedReports,
  } = getStats();

  // Get overdue complaints - same logic for both admin and contractor
  const getOverdueComplaints = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return baseStatsData.filter((complaint) => {
      if (!complaint.expected_completion_date) return false;

      const expectedDate = new Date(complaint.expected_completion_date);
      expectedDate.setHours(0, 0, 0, 0);

      // Overdue if: past expected completion date AND not yet completed/verified
      return (
        expectedDate < today &&
        complaint.follow_up !== 'verified' &&
        !complaint.actual_completion_date
      );
    });
  };

  const overdueComplaints = getOverdueComplaints();
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-800 border-green-200"
          >
            {t('status.open')}
          </Badge>
        );
      case 'on_hold':
        return (
          <Badge
            variant="outline"
            className="bg-orange-50 text-orange-800 border-orange-200"
          >
            {t('status.onHold')}
          </Badge>
        );
      case 'closed':
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-800 border-red-200"
          >
            {t('status.closed')}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getFollowUpBadge = (followUp: string) => {
    switch (followUp) {
      case 'in_progress':
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-800 border-blue-200 flex items-center gap-1"
          >
            <Clock className="h-3 w-3" />
            {t('followUp.inProgress')}
          </Badge>
        );
      case 'pending_approval':
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-800 border-yellow-200 flex items-center gap-1"
          >
            <AlertCircle className="h-3 w-3" />
            {t('followUp.pendingApproval')}
          </Badge>
        );
      case 'verified':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-800 border-green-200 flex items-center gap-1"
          >
            <CheckCircle className="h-3 w-3" />
            {t('followUp.verified')}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{followUp}</Badge>;
    }
  };

  // Verification status badge for overdue complaints section
  const getVerificationStatusBadge = (followUp?: string) => {
    switch (followUp) {
      case 'pending_approval':
      case 'in_progress':
        return (
          <Badge
            variant="secondary"
            className="bg-yellow-100 text-yellow-800 flex items-center gap-1"
          >
            <Clock className="h-3 w-3" />
            {t('status.pendingApproval')}
          </Badge>
        );
      case 'verified':
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 flex items-center gap-1"
          >
            <CheckCircle className="h-3 w-3" />
            {t('status.verified')}
          </Badge>
        );
      default:
        return <Badge variant="outline">{followUp || 'No Status'}</Badge>;
    }
  };

  const handleViewComplaint = (
    complaint: ComplaintUI | ComplaintWithRelations,
  ) => {
    // Navigate to complaint details page or open modal
    router.push(`/complaints/${complaint.id}`);
  };

  const handleEditComplaintFromTable = (
    complaint: ComplaintUI | ComplaintWithRelations,
  ) => {
    router.push(`/complaints/${complaint.id}/edit`);
  };
  const handleCreateNewComplaint = () => {
    router.push('/complaints/new');
  };

  // Handle no project selected
  if (!selectedProjectId) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex flex-col items-center gap-4 py-8">
          <AlertCircle className="h-12 w-12 text-amber-500" />
          <div className="text-center">
            <h2 className="text-lg font-semibold mb-2">No Project Selected</h2>
            <p className="text-muted-foreground">
              Please select a project to view its complaint logs.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            Loading complaints...
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex flex-col items-center gap-2 py-8">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <p className="text-red-600">Failed to load complaints</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="mb-4 sm:mb-6">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 tracking-tight">
            {t('title')}
          </h1>
          <p className="text-gray-600 mt-2 sm:mt-3 text-sm sm:text-base lg:text-lg max-w-2xl">
            {t('subtitle')}
          </p>
        </div>
        <div className="flex gap-3">
          {/* <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            {t('exportReport')}
          </Button> */}
          <Button
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            onClick={handleCreateNewComplaint}
          >
            <Plus className="h-4 w-4" />
            {t('createAduan')}
          </Button>
        </div>
      </div>
      {/* Statistics Cards - Same layout for both admin and contractor */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        {/* Stats cards - same design for both admin and contractor */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-blue-700">
                  {inProgressReports}
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  {t('stats.inProgress')}
                </p>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-orange-50 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-orange-700">
                  {pendingApprovalReports}
                </p>
                <p className="text-sm text-orange-600 mt-1">
                  {t('stats.pendingApproval')}
                </p>
              </div>
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-green-700">
                  {verifiedReports}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  {t('stats.verified')}
                </p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-purple-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-purple-700">
                  {totalReports}
                </p>
                <p className="text-sm text-purple-600 mt-1">
                  {t('stats.totalReports')}
                </p>
              </div>
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Outstanding Complaints (Urgent) - Overdue Reports - Show for both admin and contractor */}
      {overdueComplaints.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-red-600">
              {t('urgentSection.title', { count: overdueComplaints.length })}
            </CardTitle>
            <p className="text-sm text-gray-600">
              {t('urgentSection.description')}
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {overdueComplaints.map((complaint) => {
                const expectedDate = new Date(
                  complaint.expected_completion_date || '',
                );
                const daysOverdue = Math.floor(
                  (new Date().getTime() - expectedDate.getTime()) /
                    (1000 * 60 * 60 * 24),
                );

                return (
                  <div
                    key={complaint.id}
                    className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200"
                  >
                    <div className="flex items-center space-x-3">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <div>
                        <p className="font-medium text-sm">
                          {complaint.number}
                        </p>
                        <p className="text-xs text-gray-600">
                          {complaint.description}
                        </p>
                        <p className="text-xs text-red-600 font-medium">
                          {t('urgentSection.daysOverdue', {
                            days: daysOverdue,
                            plural: daysOverdue !== 1 ? 's' : '',
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {complaint.no_pma_lif}
                      </p>
                      <p className="text-xs text-gray-500">
                        {complaint.location}
                      </p>
                      <p className="text-xs text-gray-500">
                        {t('urgentSection.expectedLabel')}{' '}
                        {expectedDate.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getVerificationStatusBadge(complaint.follow_up)}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6">
        {' '}
        {/* Complaints Table - Same UI for both Admin and Contractor */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="text-lg font-semibold">
                  {t('table.title')}
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  {filteredComplaints.length > 0
                    ? `${filteredComplaints.length} complaint${filteredComplaints.length !== 1 ? 's' : ''} found`
                    : 'No complaints found'}
                </p>
              </div>
              <div className="w-full lg:w-auto lg:max-w-lg">
                <ComplaintFilterSection
                  filters={convertToComplaintFilters(filters)}
                  onFilterChange={handleRegularUserFilterChange}
                />
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-medium text-gray-700">
                    {t('table.reportId')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.dateSubmitted')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.pmaNumber')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.location')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.issueSummary')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.status')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.followUp')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.completionDate')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.actions')}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedComplaints.map(
                  (complaint: ComplaintWithRelations | ComplaintUI) => (
                    <TableRow key={complaint.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium text-blue-600">
                        {complaint.number}
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {new Date(complaint.date).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-sm">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                          {complaint.no_pma_lif || 'N/A'}
                        </code>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {complaint.location}
                      </TableCell>
                      <TableCell className="text-sm text-gray-600 max-w-xs truncate">
                        {complaint.description || 'No description'}
                      </TableCell>
                      <TableCell>{getStatusBadge(complaint.status)}</TableCell>
                      <TableCell>
                        {getFollowUpBadge(complaint.follow_up)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {complaint.actual_completion_date
                          ? new Date(
                              complaint.actual_completion_date,
                            ).toLocaleDateString()
                          : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewComplaint(complaint)}
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {userRole !== 'admin' &&
                            complaint.follow_up !== 'verified' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() =>
                                  handleEditComplaintFromTable(complaint)
                                }
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ),
                )}
              </TableBody>
            </Table>
            {/* Pagination - Same for both Admin and Contractor */}
            <div className="flex justify-between items-center p-4 border-t">
              <span className="text-sm text-gray-600">
                {t('pagination.showing', {
                  start: startIndex + 1,
                  end: Math.min(endIndex, filteredComplaints.length),
                  total: filteredComplaints.length,
                })}
              </span>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  {t('pagination.previous')}
                </Button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  const pageNumber = i + 1;
                  return (
                    <Button
                      key={pageNumber}
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageClick(pageNumber)}
                      className={
                        currentPage === pageNumber
                          ? 'bg-blue-600 text-white'
                          : ''
                      }
                    >
                      {pageNumber}
                    </Button>
                  );
                })}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages || totalPages === 0}
                >
                  {t('pagination.next')}
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
