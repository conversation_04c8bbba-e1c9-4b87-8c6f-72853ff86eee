'use client';

import {
  ComplaintsWidget,
  MaintenanceLogsWidget,
  PMACertificatesWidget,
  StatsOverview,
  TrialExpirationReminder,
} from '@/components/dashboard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useProject } from '@/features/projects';
import { useUserWithProfile } from '@/hooks';
import { usePermissions } from '@/hooks/use-permissions';
import { useAccessibleProjects } from '@/hooks/use-project-access';
import { useProjectContext } from '@/providers/project-context';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import React from 'react';

export default function DashboardPage() {
  const { data: user, isLoading, error } = useUserWithProfile();
  const { userRole } = usePermissions();
  const { selectedProjectId, isInProjectContext, selectProject } =
    useProjectContext();
  const { data: _project, isLoading: projectLoading } = useProject(
    selectedProjectId || '',
  );
  const { data: projects, isLoading: projectsLoading } =
    useAccessibleProjects();
  const router = useRouter();
  const t = useTranslations('dashboard');

  // Auto-select first project for admin users if no project is selected
  React.useEffect(() => {
    // Check if user intentionally cleared project
    const userClearedProject = sessionStorage.getItem('user_cleared_project');

    if (
      !isLoading &&
      !projectsLoading &&
      !isInProjectContext &&
      userRole === 'admin' &&
      projects &&
      projects.length > 0 &&
      !userClearedProject
    ) {
      selectProject(projects[0].id);
    }

    // Clear the flag after checking
    if (userClearedProject) {
      sessionStorage.removeItem('user_cleared_project');
    }
  }, [
    isLoading,
    projectsLoading,
    isInProjectContext,
    userRole,
    projects,
    selectProject,
  ]);

  if (isLoading || (isInProjectContext && projectLoading) || projectsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">
          {isInProjectContext ? t('loadingProject') : t('loading')}
        </div>
      </div>
    );
  }

  if (error || !user) {
    router.push('/');
    return null;
  }

  // If admin has no projects, show a message
  if (userRole === 'admin' && (!projects || projects.length === 0)) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex flex-col items-center justify-center min-h-[50vh]">
          <h2 className="text-2xl font-bold mb-4">No projects available</h2>
          <p className="text-muted-foreground mb-6">
            There are no projects available for your account.
          </p>
          <Button onClick={() => router.push('/projects')}>
            View Projects
          </Button>
        </div>
      </div>
    );
  }

  // Regular dashboard when not in project context
  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            {t('welcomeMessage', {
              name: user.profile?.name || user.email?.split('@')[0] || 'User',
            })}
          </h1>
          <p className="text-muted-foreground mt-1">
            {t('welcomeDescription')}
          </p>
          {userRole && (
            <Badge variant="secondary" className="mt-2">
              {userRole}
            </Badge>
          )}
        </div>
      </div>

      {/* Trial Expiration Reminders */}
      <div className="mb-6">
        <TrialExpirationReminder />
      </div>

      {/* Stats Overview */}
      <div className="mb-6">
        <StatsOverview />
      </div>

      {/* Overview Dashboard Widgets */}
      <div className="grid grid-cols-1 gap-8 mb-8">
        {/* PMA Certificates Widget */}
        <PMACertificatesWidget />

        {/* Maintenance Logs Widget */}
        <MaintenanceLogsWidget />

        {/* Complaints Widget */}
        <ComplaintsWidget />
      </div>
    </div>
  );
}
