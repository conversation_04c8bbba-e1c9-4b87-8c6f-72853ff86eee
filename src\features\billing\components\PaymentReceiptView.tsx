'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Download, FileText, Printer } from 'lucide-react';
import Image from 'next/image';
import { usePaymentReceipt } from '../hooks/usePaymentReceipt';

interface PaymentReceiptViewProps {
  paymentId: string;
}

export function PaymentReceiptView({ paymentId }: PaymentReceiptViewProps) {
  const {
    data: paymentRecord,
    isLoading,
    error,
  } = usePaymentReceipt({
    paymentId,
    enabled: !!paymentId,
  });

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    window.print();
  };

  if (isLoading) {
    return <PaymentReceiptSkeleton />;
  }

  if (error || !paymentRecord) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">
              Receipt Not Available
            </h2>
            <p className="text-muted-foreground">
              {error instanceof Error
                ? error.message
                : 'Unable to load receipt. Please try again later.'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 print:bg-white print:p-0">
      <div className="max-w-2xl mx-auto">
        {/* Print/Download Actions - Hidden in print */}
        <div className="mb-6 print:hidden">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Payment Receipt</h1>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </div>
        </div>

        {/* Receipt Content */}
        <Card className="print:shadow-none print:border-none">
          <CardHeader className="text-center border-b print:border-b-2">
            <div className="space-y-4">
              <div className="flex justify-center">
                <Image
                  src="/Simple-Logo-Gradient.svg"
                  alt="SimPLE Logo"
                  width={120}
                  height={30}
                  className="print:w-[120px] print:h-[30px]"
                />
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Simple Project Lifecycle Enhancement
                </p>
                <p className="text-xs text-muted-foreground">Payment Receipt</p>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            {/* Receipt Header */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Receipt Details</h3>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="text-muted-foreground">Receipt ID:</span>
                    <br />
                    <span className="font-mono">{paymentRecord.id}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Date Issued:</span>
                    <br />
                    <span>{formatDate(paymentRecord.created_at)}</span>
                  </div>
                  {paymentRecord.paid_at && (
                    <div>
                      <span className="text-muted-foreground">Date Paid:</span>
                      <br />
                      <span>{formatDate(paymentRecord.paid_at)}</span>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Project Information</h3>
                <div className="space-y-1 text-sm">
                  <div>
                    <span className="text-muted-foreground">PMA Number:</span>
                    <br />
                    <span className="font-mono">
                      {
                        paymentRecord.pma_subscription.pma_certificates
                          .pma_number
                      }
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Project:</span>
                    <br />
                    <span>
                      {
                        paymentRecord.pma_subscription.pma_certificates.projects
                          .name
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Payment Details */}
            <div>
              <h3 className="font-semibold mb-4">Payment Information</h3>
              <div className="bg-gray-50 print:bg-gray-100 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <span className="text-sm text-muted-foreground">
                      Amount:
                    </span>
                    <div className="text-2xl font-bold">
                      {formatCurrency(paymentRecord.amount)}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">
                      Status:
                    </span>
                    <div className="mt-1">
                      <Badge
                        variant="default"
                        className="bg-green-100 text-green-800"
                      >
                        Paid
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">
                      Payment Method:
                    </span>
                    <br />
                    <span>Billplz (Online Banking)</span>
                  </div>
                  {paymentRecord.billplz_bill_id && (
                    <div>
                      <span className="text-muted-foreground">
                        Reference ID:
                      </span>
                      <br />
                      <span className="font-mono">
                        {paymentRecord.billplz_bill_id}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            {/* Footer */}
            <div className="text-center text-xs text-muted-foreground pt-4 border-t">
              <p>
                This is a computer-generated receipt. No signature is required.
              </p>
              <p className="mt-1">
                Generated on {formatDate(new Date().toISOString())}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function PaymentReceiptSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
        </div>

        <Card>
          <CardHeader className="text-center border-b">
            <Skeleton className="h-6 w-24 mx-auto mb-2" />
            <Skeleton className="h-4 w-48 mx-auto mb-1" />
            <Skeleton className="h-3 w-32 mx-auto" />
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <Skeleton className="h-5 w-32" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-5 w-32" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Skeleton className="h-5 w-32" />
              <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-12 w-24" />
                  <Skeleton className="h-8 w-16" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-32" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-MY', {
    style: 'currency',
    currency: 'MYR',
  }).format(amount / 100);
}

function formatDate(dateString: string): string {
  return new Intl.DateTimeFormat('en-MY', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString));
}
