import type { PmaSubscriptionWithAccess } from '@/types/billing';

/**
 * Contractor bulk payment data for processing all PMAs at once
 */
export interface ContractorBulkPaymentData {
  contractor_id: string;
  contractor_name: string;
  total_amount: number;
  outstanding_subscriptions: PmaSubscriptionWithAccess[];
  payment_breakdown: {
    active_overdue: number;
    grace_period: number;
    suspended: number;
    trial: number;
  };
  total_pmas_count: number;
}

/**
 * Result of bulk payment processing
 */
export interface BulkPaymentResult {
  success: boolean;
  transaction_id?: string;
  processed_subscriptions: string[];
  failed_subscriptions: string[];
  total_amount_paid: number;
  error_message?: string;
}

/**
 * Summary for contractor payment display
 */
export interface ContractorPaymentSummary {
  contractor_id: string;
  contractor_name: string;
  total_outstanding_amount: number;
  pmas_requiring_payment: number;
  urgent_pmas_count: number;
  has_suspended_pmas: boolean;
  next_billing_date?: string;
}

/**
 * Payment method types
 */
export type PaymentMethodType = 'billplz_redirect' | 'saved_card' | 'new_card';

/**
 * Saved card information for display
 */
export interface SavedCardInfo {
  id: string;
  card_token: string;
  last_four: string;
  brand: string;
  exp_month: number;
  exp_year: number;
  is_default: boolean;
  created_at?: string;
}

/**
 * New card input data
 */
export interface NewCardData {
  number: string;
  exp_month: number;
  exp_year: number;
  cvc: string;
  name: string;
  save_card?: boolean;
}

/**
 * Bulk payment request payload
 */
export interface BulkPaymentRequest {
  contractor_id: string;
  user_id: string; // Specific user for billing contact info
  subscription_ids: string[];
  total_amount: number;
  payment_method: PaymentMethodType;
  card_token?: string; // Required when payment_method is 'saved_card'
  new_card_data?: NewCardData; // Required when payment_method is 'new_card'
}
