import { useProjectContext } from '@/providers/project-context';
import { useQuery } from '@tanstack/react-query';
import { fetchStatusCounts } from '../services/maintenanceLogsService';

/**
 * Hook to fetch status counts for maintenance logs (in service vs out of service)
 * This provides total counts across all records, not filtered/paginated data
 */
export function useStatusCounts() {
  const { selectedProjectId } = useProjectContext();

  return useQuery({
    queryKey: ['maintenance-status-counts', selectedProjectId],
    queryFn: () => {
      if (!selectedProjectId) {
        throw new Error('No project selected');
      }

      return fetchStatusCounts(selectedProjectId);
    },
    enabled: !!selectedProjectId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
}
