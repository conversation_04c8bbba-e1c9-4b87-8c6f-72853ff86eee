export interface OBSUploadConfig {
  file: File;
  folder?: string;
  onProgress?: (progress: number) => void;
  maxRetries?: number;
}

export async function uploadToOBS({
  file,
  folder = '',
  onProgress,
  maxRetries = 3,
}: OBSUploadConfig): Promise<string> {
  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (folder) {
        formData.append('folder', folder);
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      // Use XMLHttpRequest for progress tracking
      const result = await new Promise<string>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // Track upload progress
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress(progress);
          }
        });

        xhr.addEventListener('load', async () => {
          clearTimeout(timeoutId);

          try {
            const data = JSON.parse(xhr.responseText);

            if (xhr.status >= 200 && xhr.status < 300) {
              resolve(data.url);
            } else {
              reject(
                new Error(
                  data.error || `Upload failed with status ${xhr.status}`,
                ),
              );
            }
          } catch {
            reject(new Error('Failed to parse server response'));
          }
        });

        xhr.addEventListener('error', () => {
          clearTimeout(timeoutId);
          reject(new Error('Network error during upload'));
        });

        xhr.addEventListener('abort', () => {
          clearTimeout(timeoutId);
          reject(new Error('Upload timed out after 30 seconds'));
        });

        xhr.open('POST', '/api/upload');
        xhr.send(formData);

        // Handle timeout
        controller.signal.addEventListener('abort', () => {
          xhr.abort();
        });
      });

      return result;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');

      // If this is the last attempt, don't wait
      if (attempt < maxRetries) {
        // Wait before retrying with exponential backoff
        await new Promise((resolve) =>
          setTimeout(resolve, Math.pow(2, attempt - 1) * 1000),
        );
      }
    }
  }

  throw new Error(
    `Failed to upload file to OBS after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`,
  );
}
