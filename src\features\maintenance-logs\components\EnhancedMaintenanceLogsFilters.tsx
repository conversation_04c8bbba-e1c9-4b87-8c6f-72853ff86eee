'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { format, startOfMonth, startOfWeek, subDays } from 'date-fns';
import {
  CalendarIcon,
  Check,
  ChevronDown,
  ChevronUp,
  Filter,
  Search,
  Settings2,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  MAINTENANCE_STATUS,
  OPERATION_LOG_TYPES,
} from '../schemas/create-maintenance-log';
import type { MaintenanceLogsFilters } from '../types/table';

interface EnhancedMaintenanceLogsFiltersProps {
  onFilterChange: (filters: MaintenanceLogsFilters) => void;
  filters: MaintenanceLogsFilters;
  isLoading?: boolean;
  contractors?: Array<{ id: string; name: string }>;
  personsInCharge?: Array<{ id: string; name: string }>;
}

// Mock data for demonstration
const MOCK_CONTRACTORS = [
  { id: '1', name: 'ABC Maintenance Sdn Bhd' },
  { id: '2', name: 'XYZ Engineering Services' },
  { id: '3', name: 'Professional Lift Solutions' },
  { id: '4', name: 'Metro Elevator Services' },
  { id: '5', name: 'Elite Maintenance Corp' },
];

const MOCK_PERSONS_IN_CHARGE = [
  { id: '1', name: 'Ahmad Rahman' },
  { id: '2', name: 'Siti Nurhaliza' },
  { id: '3', name: 'John Smith' },
  { id: '4', name: 'Maria Garcia' },
  { id: '5', name: 'David Chen' },
];

// Date range presets
const DATE_PRESETS = [
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'thisWeek' },
  { label: 'This Month', value: 'thisMonth' },
  { label: 'Last 30 Days', value: 'last30Days' },
  { label: 'Custom Range', value: 'custom' },
] as const;

// Status options with visual indicators
const STATUS_OPTIONS = [
  {
    value: 'in service',
    label: 'In Service',
    color: 'bg-green-500',
    bgColor: 'bg-green-50 text-green-700 border-green-200',
  },
  {
    value: 'out of service',
    label: 'Out of Service',
    color: 'bg-red-500',
    bgColor: 'bg-red-50 text-red-700 border-red-200',
  },
] as const;

export function EnhancedMaintenanceLogsFilters({
  onFilterChange,
  filters,
  isLoading: _isLoading = false,
  contractors = MOCK_CONTRACTORS,
  personsInCharge = MOCK_PERSONS_IN_CHARGE,
}: EnhancedMaintenanceLogsFiltersProps) {
  const _t = useTranslations('pages.maintenanceLogs');
  const _common = useTranslations('common');

  // State management
  const [debouncedSearch, setDebouncedSearch] = useState(filters.search || '');
  const [contractorSearch, setContractorSearch] = useState('');
  const [personInChargeSearch, setPersonInChargeSearch] = useState('');
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [selectedOperationTypes, setSelectedOperationTypes] = useState<
    string[]
  >(filters.operationType ? [filters.operationType] : []);
  const [showClearConfirmation, setShowClearConfirmation] = useState(false);

  // Helper functions for date presets
  const getDateRangeFromPreset = useCallback((preset: string) => {
    const today = new Date();
    switch (preset) {
      case 'today':
        return { from: today, to: today };
      case 'thisWeek':
        return { from: startOfWeek(today), to: today };
      case 'thisMonth':
        return { from: startOfMonth(today), to: today };
      case 'last30Days':
        return { from: subDays(today, 30), to: today };
      default:
        return undefined;
    }
  }, []);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (selectedOperationTypes.length > 0) count++;
    if (filters.status) count++;
    if (filters.dateRange?.from) count++;
    if (filters.search) count++;
    if (filters.contractorId) count++;
    if (filters.pmaId) count++;
    return count;
  }, [selectedOperationTypes, filters]);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onFilterChange({
        ...filters,
        search: debouncedSearch,
      });
    }, 300);

    return () => clearTimeout(timer);
  }, [debouncedSearch, filters, onFilterChange]);

  // Event handlers
  const handleOperationTypeToggle = useCallback(
    (type: string) => {
      const newTypes = selectedOperationTypes.includes(type)
        ? selectedOperationTypes.filter((t) => t !== type)
        : [...selectedOperationTypes, type];

      setSelectedOperationTypes(newTypes);
      onFilterChange({
        ...filters,
        operationType:
          newTypes.length === 1
            ? (newTypes[0] as (typeof OPERATION_LOG_TYPES)[number])
            : undefined,
      });
    },
    [selectedOperationTypes, filters, onFilterChange],
  );

  const handleSelectAllOperationTypes = useCallback(() => {
    const allSelected =
      selectedOperationTypes.length === OPERATION_LOG_TYPES.length;
    const newTypes = allSelected ? [] : [...OPERATION_LOG_TYPES];
    setSelectedOperationTypes(newTypes);
    onFilterChange({
      ...filters,
      operationType: undefined,
    });
  }, [selectedOperationTypes, filters, onFilterChange]);

  const handleStatusChange = useCallback(
    (value: string) => {
      onFilterChange({
        ...filters,
        status:
          value === 'all'
            ? undefined
            : (value as (typeof MAINTENANCE_STATUS)[number]),
      });
    },
    [filters, onFilterChange],
  );

  const handleDatePresetChange = useCallback(
    (preset: string) => {
      if (preset === 'custom') return;
      const dateRange = getDateRangeFromPreset(preset);
      onFilterChange({
        ...filters,
        dateRange,
      });
    },
    [filters, onFilterChange, getDateRangeFromPreset],
  );

  const handleDateRangeChange = useCallback(
    (dateRange: { from?: Date; to?: Date }) => {
      onFilterChange({
        ...filters,
        dateRange,
      });
    },
    [filters, onFilterChange],
  );

  const clearAllFilters = useCallback(() => {
    setSelectedOperationTypes([]);
    setDebouncedSearch('');
    setContractorSearch('');
    setPersonInChargeSearch('');
    setShowClearConfirmation(false);
    onFilterChange({
      operationType: undefined,
      status: undefined,
      dateRange: undefined,
      search: '',
      contractorId: undefined,
      pmaId: undefined,
    });
  }, [onFilterChange]);

  const removeFilter = useCallback(
    (filterType: string) => {
      switch (filterType) {
        case 'operationType':
          setSelectedOperationTypes([]);
          onFilterChange({ ...filters, operationType: undefined });
          break;
        case 'status':
          onFilterChange({ ...filters, status: undefined });
          break;
        case 'dateRange':
          onFilterChange({ ...filters, dateRange: undefined });
          break;
        case 'search':
          setDebouncedSearch('');
          onFilterChange({ ...filters, search: '' });
          break;
        case 'contractor':
          onFilterChange({ ...filters, contractorId: undefined });
          break;
        case 'personInCharge':
          onFilterChange({ ...filters, pmaId: undefined });
          break;
      }
    },
    [filters, onFilterChange],
  );

  // Filter chips data
  const activeFilters = useMemo(() => {
    const chips = [];

    if (selectedOperationTypes.length > 0) {
      chips.push({
        key: 'operationType',
        label: `Type: ${selectedOperationTypes.join(', ')}`,
        onRemove: () => removeFilter('operationType'),
      });
    }

    if (filters.status) {
      const statusOption = STATUS_OPTIONS.find(
        (s) => s.value === filters.status,
      );
      chips.push({
        key: 'status',
        label: `Status: ${statusOption?.label || filters.status}`,
        onRemove: () => removeFilter('status'),
      });
    }

    if (filters.dateRange?.from) {
      const label = filters.dateRange.to
        ? `${format(filters.dateRange.from, 'MMM dd')} - ${format(filters.dateRange.to, 'MMM dd')}`
        : format(filters.dateRange.from, 'MMM dd, yyyy');
      chips.push({
        key: 'dateRange',
        label: `Date: ${label}`,
        onRemove: () => removeFilter('dateRange'),
      });
    }

    if (filters.search) {
      chips.push({
        key: 'search',
        label: `Search: "${filters.search}"`,
        onRemove: () => removeFilter('search'),
      });
    }

    return chips;
  }, [selectedOperationTypes, filters, removeFilter]);

  return (
    <Card className="border-0 bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/20 shadow-sm backdrop-blur-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg">
              <Filter className="h-5 w-5" />
            </div>
            Filters
            {activeFilterCount > 0 && (
              <Badge
                variant="secondary"
                className="ml-2 bg-blue-100 text-blue-700"
              >
                {activeFilterCount} active
              </Badge>
            )}
          </CardTitle>

          {activeFilterCount > 0 && (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowClearConfirmation(true)}
                className="text-gray-500 hover:text-gray-700"
              >
                Clear all
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Filter Chips */}
        {activeFilters.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {activeFilters.map((filter) => (
              <Badge
                key={filter.key}
                variant="secondary"
                className="bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 transition-colors"
              >
                {filter.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-2 hover:bg-transparent"
                  onClick={filter.onRemove}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        )}

        {/* Basic Filters */}
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {/* Search Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by description, person, contractor, type, status..."
                value={debouncedSearch}
                onChange={(e) => setDebouncedSearch(e.target.value)}
                className="pl-10 bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors"
              />
            </div>
          </div>

          {/* Operation Type Multi-Select */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              Operation Type
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors"
                >
                  <span className="truncate">
                    {selectedOperationTypes.length === 0
                      ? 'Select types...'
                      : selectedOperationTypes.length === 1
                        ? selectedOperationTypes[0]
                        : `${selectedOperationTypes.length} selected`}
                  </span>
                  <ChevronDown className="h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0" align="start">
                <Command>
                  <CommandList>
                    <CommandGroup>
                      <CommandItem onSelect={handleSelectAllOperationTypes}>
                        <Checkbox
                          checked={
                            selectedOperationTypes.length ===
                            OPERATION_LOG_TYPES.length
                          }
                          className="mr-2"
                        />
                        Select All
                      </CommandItem>
                      <Separator className="my-1" />
                      {OPERATION_LOG_TYPES.map((type) => (
                        <CommandItem
                          key={type}
                          onSelect={() => handleOperationTypeToggle(type)}
                        >
                          <Checkbox
                            checked={selectedOperationTypes.includes(type)}
                            className="mr-2"
                          />
                          {type}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Status Filter with Visual Indicators */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Status</Label>
            <Select
              value={filters.status || 'all'}
              onValueChange={handleStatusChange}
            >
              <SelectTrigger className="w-full bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors">
                <SelectValue placeholder="Select status..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                {STATUS_OPTIONS.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    <div className="flex items-center gap-2">
                      <div
                        className={cn('w-2 h-2 rounded-full', status.color)}
                      />
                      {status.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date Range with Presets */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              Date Range
            </Label>
            <div className="flex gap-2">
              <Select onValueChange={handleDatePresetChange}>
                <SelectTrigger className="w-full bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors">
                  <SelectValue placeholder="Quick select..." />
                </SelectTrigger>
                <SelectContent>
                  {DATE_PRESETS.map((preset) => (
                    <SelectItem key={preset.value} value={preset.value}>
                      {preset.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors',
                      !filters.dateRange && 'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateRange?.from ? (
                      filters.dateRange.to ? (
                        <>
                          {format(filters.dateRange.from, 'MMM dd')} -{' '}
                          {format(filters.dateRange.to, 'MMM dd, y')}
                        </>
                      ) : (
                        format(filters.dateRange.from, 'MMM dd, y')
                      )
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="range"
                    defaultMonth={filters.dateRange?.from}
                    selected={{
                      from: filters.dateRange?.from,
                      to: filters.dateRange?.to,
                    }}
                    onSelect={(range) => handleDateRangeChange(range || {})}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Advanced Filters Section */}
        <div className="space-y-4">
          <Button
            variant="ghost"
            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
            className="w-full justify-between p-0 h-auto text-gray-600 hover:text-gray-800"
          >
            <div className="flex items-center gap-2">
              <Settings2 className="h-4 w-4" />
              <span className="font-medium">Advanced Filters</span>
            </div>
            {isAdvancedOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>

          {isAdvancedOpen && (
            <div className="grid gap-4 sm:grid-cols-2 pt-4 border-t border-gray-100 animate-in slide-in-from-top-2 duration-200">
              {/* Contractor Filter with Autocomplete */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Contractor
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors"
                    >
                      <span className="truncate">
                        {filters.contractorId
                          ? contractors.find(
                              (c) => c.id === filters.contractorId,
                            )?.name || 'Unknown'
                          : 'Select contractor...'}
                      </span>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command>
                      <CommandInput
                        placeholder="Search contractors..."
                        value={contractorSearch}
                        onValueChange={setContractorSearch}
                      />
                      <CommandList>
                        <CommandEmpty>No contractors found.</CommandEmpty>
                        <CommandGroup>
                          {contractors
                            .filter((contractor) =>
                              contractor.name
                                .toLowerCase()
                                .includes(contractorSearch.toLowerCase()),
                            )
                            .map((contractor) => (
                              <CommandItem
                                key={contractor.id}
                                onSelect={() => {
                                  onFilterChange({
                                    ...filters,
                                    contractorId:
                                      contractor.id === filters.contractorId
                                        ? undefined
                                        : contractor.id,
                                  });
                                }}
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    filters.contractorId === contractor.id
                                      ? 'opacity-100'
                                      : 'opacity-0',
                                  )}
                                />
                                {contractor.name}
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Person in Charge Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Person in Charge
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors"
                    >
                      <span className="truncate">
                        {filters.pmaId
                          ? personsInCharge.find((p) => p.id === filters.pmaId)
                              ?.name || 'Unknown'
                          : 'Select person...'}
                      </span>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command>
                      <CommandInput
                        placeholder="Search persons..."
                        value={personInChargeSearch}
                        onValueChange={setPersonInChargeSearch}
                      />
                      <CommandList>
                        <CommandEmpty>No persons found.</CommandEmpty>
                        <CommandGroup>
                          {personsInCharge
                            .filter((person) =>
                              person.name
                                .toLowerCase()
                                .includes(personInChargeSearch.toLowerCase()),
                            )
                            .map((person) => (
                              <CommandItem
                                key={person.id}
                                onSelect={() => {
                                  onFilterChange({
                                    ...filters,
                                    pmaId:
                                      person.id === filters.pmaId
                                        ? undefined
                                        : person.id,
                                  });
                                }}
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    filters.pmaId === person.id
                                      ? 'opacity-100'
                                      : 'opacity-0',
                                  )}
                                />
                                {person.name}
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          )}
        </div>

        {/* Clear All Confirmation Modal */}
        {showClearConfirmation && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardHeader>
                <CardTitle>Clear All Filters</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Are you sure you want to clear all active filters? This action
                  cannot be undone.
                </p>
                <div className="flex gap-2 justify-end">
                  <Button
                    variant="outline"
                    onClick={() => setShowClearConfirmation(false)}
                  >
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={clearAllFilters}>
                    Clear All
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
