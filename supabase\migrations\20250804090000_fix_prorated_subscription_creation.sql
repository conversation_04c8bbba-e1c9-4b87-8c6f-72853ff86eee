-- ================================
-- FIX PMA SUBSCRIPTION PRO-RATED CREATION
-- Migration to fix create_pma_subscription_with_trial function
-- to calculate pro-rated amount at subscription creation time
-- ================================

-- ================================
-- 1. UPDATE SUBSCRIPTION CREATION FUNCTION
-- ================================

-- Drop and recreate the function with pro-rated calculation at creation
DROP FUNCTION IF EXISTS public.create_pma_subscription_with_trial(UUID, UUID, NUMERIC);

CREATE OR REPLACE FUNCTION public.create_pma_subscription_with_trial(
    p_pma_certificate_id UUID,
    p_contractor_id UUID,
    p_amount NUMERIC(10,2) DEFAULT 150.00
) RETURNS UUID AS $$
DECLARE
    subscription_id UUID;
    contractor_created_at TIMESTAMPTZ;
    is_trial_eligible BOOLEAN;
    trial_end_date TIMESTAMPTZ;
    subscription_status subscription_status;
    calculated_amount_value NUMERIC(10,2);
BEGIN
    -- Check trial eligibility
    SELECT public.is_contractor_trial_eligible(p_contractor_id) INTO is_trial_eligible;
    
    -- Get contractor creation date for trial end calculation
    SELECT created_at INTO contractor_created_at 
    FROM public.contractors 
    WHERE id = p_contractor_id AND deleted_at IS NULL;
    
    -- Set subscription parameters based on trial eligibility
    IF is_trial_eligible THEN
        subscription_status := 'trial';
        trial_end_date := contractor_created_at + INTERVAL '15 days';
        -- For trial subscriptions, use full amount
        calculated_amount_value := p_amount;
    ELSE
        subscription_status := 'pending_payment';
        trial_end_date := NULL;
        -- For pending_payment subscriptions, calculate pro-rated amount immediately
        calculated_amount_value := public.calculate_prorated_pma_amount(NOW(), CURRENT_DATE);
    END IF;
    
    -- Create the subscription with correct calculated_amount
    INSERT INTO public.pma_subscriptions (
        pma_certificate_id,
        contractor_id,
        amount,
        calculated_amount,
        status,
        trial_ends_at,
        created_at,
        updated_at
    ) VALUES (
        p_pma_certificate_id,
        p_contractor_id,
        p_amount,
        calculated_amount_value,  -- Now properly calculated based on status
        subscription_status,
        trial_end_date,
        NOW(),
        NOW()
    ) RETURNING id INTO subscription_id;
    
    RETURN subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Update function comment
COMMENT ON FUNCTION public.create_pma_subscription_with_trial IS 'Creates PMA subscription with automatic trial period detection and immediate pro-rated amount calculation for pending_payment subscriptions. Sets trial status and end date for contractors created within 15 days.';

-- ================================
-- 2. UPDATE EXISTING PENDING_PAYMENT SUBSCRIPTIONS
-- ================================

-- Update any existing pending_payment subscriptions that have incorrect calculated_amount
UPDATE public.pma_subscriptions 
SET 
    calculated_amount = public.calculate_prorated_pma_amount(created_at, CURRENT_DATE),
    updated_at = NOW()
WHERE status = 'pending_payment' 
AND calculated_amount = amount  -- Only update if calculated_amount equals full amount
AND created_at > CURRENT_DATE - INTERVAL '1 month';  -- Only recent subscriptions

-- ================================
-- 3. ADD VALIDATION CONSTRAINT
-- ================================

-- Add a constraint to ensure pending_payment subscriptions don't have calculated_amount = amount
-- (unless it's legitimately the same due to pro-rating calculation)
-- This helps catch future bugs where pro-rating isn't applied

-- Note: We won't add a strict constraint since legitimate cases might exist
-- where the pro-rated amount equals the full amount (e.g., created on 1st of month)

-- ================================
-- MIGRATION COMPLETE
-- Migration fixes:
-- 1. Updated create_pma_subscription_with_trial to calculate pro-rated amount at creation
-- 2. Fixed existing pending_payment subscriptions with incorrect calculated_amount
-- 3. Added improved documentation
-- ================================