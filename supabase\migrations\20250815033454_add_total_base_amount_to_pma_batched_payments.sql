-- Add total_base_amount column to pma_batched_payments table with default 0
ALTER TABLE public.pma_batched_payments 
ADD COLUMN total_base_amount numeric(10, 2) NOT NULL DEFAULT 0;

-- For existing records, calculate and update total_base_amount based on linked payment records
UPDATE public.pma_batched_payments 
SET total_base_amount = (
    SELECT COALESCE(SUM(ps.amount), 0)
    FROM public.pma_payment_records ppr
    JOIN public.pma_subscriptions ps ON ppr.pma_subscription_id = ps.id
    WHERE ppr.batched_payment_id = pma_batched_payments.id
)
WHERE total_base_amount = 0;

-- Add constraint to ensure total_base_amount is positive for new records
ALTER TABLE public.pma_batched_payments 
ADD CONSTRAINT chk_batched_payment_base_amount_positive 
CHECK (total_base_amount >= 0);

-- Add comment to clarify the difference between the amounts
COMMENT ON COLUMN public.pma_batched_payments.total_amount IS 'Pro-rated amount for the batch payment';
COMMENT ON COLUMN public.pma_batched_payments.total_base_amount IS 'Total base amount of PMA subscriptions before pro-rating';