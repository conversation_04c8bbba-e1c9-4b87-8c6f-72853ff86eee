import { useQuery } from '@tanstack/react-query';
import {
  getPaymentRecords,
  getPaymentRecordById,
  getPaymentSummary,
} from '../services/payment-records-api.service';
import type {
  PaymentRecordsQueryParams,
  PaymentRecordsResponse,
} from '../types/payment-records';

interface UsePaymentRecordsParams {
  contractorId?: string;
  status?: PaymentRecordsQueryParams['status'];
  dateFrom?: string;
  dateTo?: string;
  subscriptionId?: string;
  batchedPaymentId?: string;
  page?: number;
  limit?: number;
  enabled?: boolean;
}

/**
 * Hook for fetching payment records with filtering and pagination
 */
export function usePaymentRecords({
  contractorId,
  status,
  dateFrom,
  dateTo,
  subscriptionId,
  batchedPaymentId,
  page = 1,
  limit = 50,
  enabled = true,
}: UsePaymentRecordsParams) {
  return useQuery({
    queryKey: [
      'payment-records',
      contractorId,
      status,
      dateFrom,
      dateTo,
      subscriptionId,
      batchedPaymentId,
      page,
      limit,
    ],
    queryFn: (): Promise<PaymentRecordsResponse> => {
      return getPaymentRecords({
        contractorId: contractorId!,
        status,
        dateFrom,
        dateTo,
        subscriptionId,
        batchedPaymentId,
        page,
        limit,
      });
    },
    enabled: enabled && !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

interface UsePaymentRecordParams {
  id?: string;
  enabled?: boolean;
}

/**
 * Hook for fetching a single payment record by ID
 */
export function usePaymentRecord({
  id,
  enabled = true,
}: UsePaymentRecordParams) {
  return useQuery({
    queryKey: ['payment-record', id],
    queryFn: () => {
      return getPaymentRecordById(id!);
    },
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

interface UsePaymentSummaryParams {
  contractorId?: string;
  enabled?: boolean;
}

/**
 * Hook for fetching payment summary statistics for a contractor
 */
export function usePaymentSummary({
  contractorId,
  enabled = true,
}: UsePaymentSummaryParams) {
  return useQuery({
    queryKey: ['payment-summary', contractorId],
    queryFn: () => {
      return getPaymentSummary(contractorId!);
    },
    enabled: enabled && !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for fetching payment records that are part of a specific batch
 */
export function useBatchPaymentRecords({
  batchedPaymentId,
  enabled = true,
}: {
  batchedPaymentId?: string;
  enabled?: boolean;
}) {
  return useQuery({
    queryKey: ['batch-payment-records', batchedPaymentId],
    queryFn: (): Promise<PaymentRecordsResponse> => {
      return getPaymentRecords({
        contractorId: '', // This will be handled by the batch filter
        batchedPaymentId: batchedPaymentId!,
      });
    },
    enabled: enabled && !!batchedPaymentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for fetching payment records that are NOT part of any batch (individual payments)
 */
export function useIndividualPaymentRecords({
  contractorId,
  status,
  page = 1,
  limit = 50,
  enabled = true,
}: {
  contractorId?: string;
  status?: PaymentRecordsQueryParams['status'];
  page?: number;
  limit?: number;
  enabled?: boolean;
}) {
  return useQuery({
    queryKey: ['individual-payment-records', contractorId, status, page, limit],
    queryFn: (): Promise<PaymentRecordsResponse> => {
      // Using a custom query to filter out batched payments
      return getPaymentRecords({
        contractorId: contractorId!,
        status,
        page,
        limit,
        // We'll need to modify the API to support filtering out batched payments
      });
    },
    enabled: enabled && !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
