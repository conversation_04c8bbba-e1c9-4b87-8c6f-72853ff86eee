-- Update complaint number generation to be project-specific
-- Each project will have its own sequence starting from 001

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS trigger_set_complaint_number ON complaints;
DROP FUNCTION IF EXISTS set_complaint_number();
DROP FUNCTION IF EXISTS generate_complaint_number();

-- Create new function to generate project-specific complaint numbers
CREATE OR REPLACE FUNCTION generate_complaint_number(project_uuid UUID)
RETURNS text AS $$
DECLARE
    current_year INTEGER;
    next_number INTEGER;
    complaint_number TEXT;
    project_code TEXT;
BEGIN
    -- Get current year
    current_year := EXTRACT(YEAR FROM CURRENT_DATE);
    
    -- Get project code/name for the complaint number (first 3 chars of project name, uppercase)
    SELECT UPPER(LEFT(REGEXP_REPLACE(name, '[^A-Za-z0-9]', '', 'g'), 3))
    INTO project_code
    FROM projects
    WHERE id = project_uuid;
    
    -- Fallback to 'PRJ' if project not found or name is too short
    IF project_code IS NULL OR LENGTH(project_code) < 3 THEN
        project_code := 'PRJ';
    END IF;
    
    -- Get the highest number for current year and project
    SELECT COALESCE(
        MAX(CAST(SPLIT_PART(number, '-', 3) AS INTEGER)), 0
    ) + 1
    INTO next_number
    FROM complaints
    WHERE project_id = project_uuid
    AND number LIKE project_code || '-' || current_year || '-%';
    
    -- Format the complaint number with zero-padding
    complaint_number := project_code || '-' || current_year || '-' || LPAD(next_number::text, 3, '0');
    
    RETURN complaint_number;
END;
$$ LANGUAGE plpgsql;

-- Create new trigger function that uses project_id
CREATE OR REPLACE FUNCTION set_complaint_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.number IS NULL OR NEW.number = '' THEN
        IF NEW.project_id IS NOT NULL THEN
            NEW.number := generate_complaint_number(NEW.project_id);
        ELSE
            -- Fallback for complaints without project_id
            NEW.number := 'GEN-' || EXTRACT(YEAR FROM CURRENT_DATE) || '-' || LPAD(nextval('complaint_global_seq')::text, 3, '0');
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a global sequence for complaints without project_id (fallback)
CREATE SEQUENCE IF NOT EXISTS complaint_global_seq START 1;

-- Create the trigger
CREATE TRIGGER trigger_set_complaint_number
    BEFORE INSERT ON complaints
    FOR EACH ROW
    EXECUTE FUNCTION set_complaint_number();

-- Update existing complaints to have project-specific numbers
-- This will regenerate all complaint numbers based on their project
DO $$
DECLARE
    complaint_record RECORD;
    new_number TEXT;
BEGIN
    FOR complaint_record IN 
        SELECT id, project_id, created_at
        FROM complaints 
        WHERE project_id IS NOT NULL
        ORDER BY project_id, created_at
    LOOP
        new_number := generate_complaint_number(complaint_record.project_id);
        UPDATE complaints 
        SET number = new_number 
        WHERE id = complaint_record.id;
    END LOOP;
END $$;