import type { Database } from '@/types/database';

export type PaymentStatus = Database['public']['Enums']['payment_status'];

export interface PaymentRecord {
  id: string;
  pma_subscription_id: string;
  billplz_bill_id: string | null;
  amount: number;
  status: PaymentStatus;
  paid_at: string | null;
  failure_reason: string | null;
  billplz_response: Record<string, unknown> | null;
  created_at: string;
  batched_payment_id?: string | null;
}

export interface PaymentRecordWithSubscription extends PaymentRecord {
  pma_subscription: {
    id: string;
    contractor_id: string;
    pma_certificates: {
      id: string;
      pma_number: string;
      projects: {
        id: string;
        name: string;
      };
    };
  };
}

export interface PaymentRecordsFilters {
  status?: PaymentStatus;
  dateFrom?: string;
  dateTo?: string;
  subscriptionId?: string;
  batchedPaymentId?: string;
}

export interface PaymentRecordsResponse {
  records: PaymentRecordWithSubscription[];
  total: number;
  hasMore: boolean;
}

export interface PaymentRecordsQueryParams extends PaymentRecordsFilters {
  contractorId: string;
  page?: number;
  limit?: number;
}

// ================================
// BATCHED PAYMENTS INTERFACES
// ================================

export interface BatchedPayment {
  id: string;
  batch_bill_id: string | null;
  contractor_id: string;
  total_amount: number;
  total_base_amount: number;
  total_pma_count: number;
  pma_numbers: string[];
  status: PaymentStatus;
  paid_at: string | null;
  failure_reason: string | null;
  billplz_response: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
}

export interface BatchedPaymentWithRecords extends BatchedPayment {
  payment_records: PaymentRecordWithSubscription[];
}

export interface PaymentRecordWithBatch extends PaymentRecordWithSubscription {
  batched_payment?: BatchedPayment | null;
}

export interface BatchedPaymentsFilters {
  status?: PaymentStatus;
  dateFrom?: string;
  dateTo?: string;
  pmaCount?: number;
}

export interface BatchedPaymentsResponse {
  batches: BatchedPayment[];
  total: number;
  hasMore: boolean;
}

export interface BatchedPaymentsQueryParams extends BatchedPaymentsFilters {
  contractorId: string;
  page?: number;
  limit?: number;
}

export interface CreateBatchedPaymentRequest {
  contractor_id: string;
  subscription_ids: string[];
  total_amount: number;
  pma_numbers: string[];
  batch_bill_id?: string;
}
