import { useToast } from '@/hooks/use-toast';
import { SubscriptionError } from '@/lib/subscription-utils';
import { useProjectContext } from '@/providers/project-context';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  createMaintenanceLog,
  deleteMaintenanceLog,
  updateMaintenanceLog,
} from '../services/maintenanceLogsService';
import type { MaintenanceLogsType } from '../types/table';

export function useCreateMaintenanceLog() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { selectedProjectId } = useProjectContext();

  return useMutation({
    mutationFn: createMaintenanceLog,
    onSuccess: () => {
      // Invalidate and refetch maintenance logs and status counts
      queryClient.invalidateQueries({
        queryKey: ['maintenance-logs', selectedProjectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['maintenance-status-counts', selectedProjectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['maintenance-stats', selectedProjectId],
      });

      // Invalidate dashboard stats to update the dashboard overview
      queryClient.invalidateQueries({
        queryKey: ['dashboard', 'maintenance-logs', selectedProjectId],
      });

      toast({
        title: 'Success',
        description: 'Maintenance log created successfully',
      });
    },
    onError: (error: Error) => {
      let title = 'Error';
      let description = `Failed to create maintenance log: ${error.message}`;

      // Handle subscription errors specially
      if (error instanceof SubscriptionError) {
        title = 'Subscription Required';
        description = error.message;

        if (error.status === 'grace_period' && error.gracePeriodEnds) {
          description += `. Grace period ends: ${new Date(error.gracePeriodEnds).toLocaleDateString()}`;
        }
      }

      toast({
        title,
        description,
        variant: 'destructive',
      });
    },
  });
}

export function useUpdateMaintenanceLog() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { selectedProjectId } = useProjectContext();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: Partial<MaintenanceLogsType>;
    }) => updateMaintenanceLog(id, data),
    onSuccess: () => {
      // Invalidate and refetch maintenance logs and status counts
      queryClient.invalidateQueries({
        queryKey: ['maintenance-logs', selectedProjectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['maintenance-status-counts', selectedProjectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['maintenance-stats', selectedProjectId],
      });

      // Invalidate dashboard stats to update the dashboard overview
      queryClient.invalidateQueries({
        queryKey: ['dashboard', 'maintenance-logs', selectedProjectId],
      });

      toast({
        title: 'Success',
        description: 'Maintenance log updated successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to update maintenance log: ${error.message}`,
        variant: 'destructive',
      });
    },
  });
}

export function useDeleteMaintenanceLog() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { selectedProjectId } = useProjectContext();

  return useMutation({
    mutationFn: deleteMaintenanceLog,
    onSuccess: () => {
      // Invalidate and refetch maintenance logs and status counts
      queryClient.invalidateQueries({
        queryKey: ['maintenance-logs', selectedProjectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['maintenance-status-counts', selectedProjectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['maintenance-stats', selectedProjectId],
      });

      // Invalidate dashboard stats to update the dashboard overview
      queryClient.invalidateQueries({
        queryKey: ['dashboard', 'maintenance-logs', selectedProjectId],
      });

      toast({
        title: 'Success',
        description: 'Maintenance log deleted successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to delete maintenance log: ${error.message}`,
        variant: 'destructive',
      });
    },
  });
}
