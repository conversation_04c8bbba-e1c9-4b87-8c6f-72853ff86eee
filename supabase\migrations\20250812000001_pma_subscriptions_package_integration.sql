-- ================================
-- PMA SUBSCRIPTIONS PACKAGE INTEGRATION
-- Integrate package pricing with PMA subscriptions system
-- ================================

-- Create function to get amount based on contractor package
CREATE OR REPLACE FUNCTION get_pma_subscription_amount(contractor_id_param uuid)
RETURNS numeric(10, 2)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN get_contractor_pricing(contractor_id_param);
END;
$$;

-- Create trigger function to automatically set amount based on contractor package
CREATE OR REPLACE FUNCTION set_pma_subscription_amount()
RETURNS TRIGGER AS $$
BEGIN
    -- Set amount and calculated_amount based on contractor's package
    NEW.amount = get_contractor_pricing(NEW.contractor_id);
    NEW.calculated_amount = NEW.amount;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger to automatically set amount on insert/update
CREATE TRIGGER trigger_set_pma_subscription_amount
    BEFORE INSERT OR UPDATE OF contractor_id ON pma_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION set_pma_subscription_amount();

-- Create function to update subscription amounts when contractor package changes
CREATE OR REPLACE FUNCTION update_pma_subscriptions_on_package_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if package_id has actually changed
    IF OLD.package_id IS DISTINCT FROM NEW.package_id THEN
        
        -- Update all PMA subscriptions for this contractor with new package pricing
        UPDATE pma_subscriptions 
        SET 
            amount = get_contractor_pricing(NEW.id),
            calculated_amount = get_contractor_pricing(NEW.id),
            updated_at = NOW()
        WHERE contractor_id = NEW.id;
        
        -- Log the update for debugging
        RAISE NOTICE 'Updated % PMA subscription(s) for contractor % due to package change', 
            (SELECT COUNT(*) FROM pma_subscriptions WHERE contractor_id = NEW.id),
            NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update subscriptions when contractor package changes
CREATE TRIGGER trigger_update_subscriptions_on_package_change
    AFTER UPDATE OF package_id ON contractors
    FOR EACH ROW
    EXECUTE FUNCTION update_pma_subscriptions_on_package_change();

-- Create function to handle package price changes
CREATE OR REPLACE FUNCTION update_subscriptions_on_package_price_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if pricing has actually changed
    IF OLD.pricing IS DISTINCT FROM NEW.pricing THEN
        
        -- Update all PMA subscriptions for contractors using this package
        UPDATE pma_subscriptions 
        SET 
            amount = NEW.pricing,
            calculated_amount = NEW.pricing,
            updated_at = NOW()
        WHERE contractor_id IN (
            SELECT id FROM contractors WHERE package_id = NEW.id
        );
        
        -- Log the update for debugging
        RAISE NOTICE 'Updated PMA subscriptions for % contractor(s) due to package % price change from % to %', 
            (SELECT COUNT(*) FROM contractors WHERE package_id = NEW.id),
            NEW.name,
            OLD.pricing,
            NEW.pricing;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update subscriptions when package pricing changes
CREATE TRIGGER trigger_update_subscriptions_on_package_price_change
    AFTER UPDATE OF pricing ON contractors_package
    FOR EACH ROW
    EXECUTE FUNCTION update_subscriptions_on_package_price_change();

-- Update existing pma_subscriptions to use contractor package pricing
UPDATE pma_subscriptions 
SET 
    amount = get_contractor_pricing(contractor_id),
    calculated_amount = get_contractor_pricing(contractor_id)
WHERE contractor_id IS NOT NULL;

-- Add comments
COMMENT ON TRIGGER trigger_set_pma_subscription_amount ON pma_subscriptions IS 'Automatically sets amount and calculated_amount based on contractor package pricing';
COMMENT ON TRIGGER trigger_update_subscriptions_on_package_change ON contractors IS 'Updates PMA subscription amounts when contractor package assignment changes';
COMMENT ON TRIGGER trigger_update_subscriptions_on_package_price_change ON contractors_package IS 'Updates PMA subscription amounts when package pricing changes';