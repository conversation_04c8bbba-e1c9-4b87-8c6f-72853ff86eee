import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';

type ContractorPackage =
  Database['public']['Tables']['contractors_package']['Row'];
type ContractorPackageInsert =
  Database['public']['Tables']['contractors_package']['Insert'];
type ContractorPackageUpdate =
  Database['public']['Tables']['contractors_package']['Update'];

export interface ContractorPackageWithUsageCount extends ContractorPackage {
  usage_count: number;
}

export class ContractorPackageService {
  /**
   * Get all contractor packages, optionally filtered by active status
   */
  static async getAll(includeInactive = false): Promise<{
    success: boolean;
    data?: ContractorPackage[];
    error?: string;
  }> {
    try {
      let query = supabase.from('contractors_package').select('*');

      if (!includeInactive) {
        query = query.eq('is_active', true);
      }

      const { data, error } = await query.order('name');

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data: data || [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get all packages with usage count (how many contractors are using each package)
   */
  static async getAllWithUsageCount(): Promise<{
    success: boolean;
    data?: ContractorPackageWithUsageCount[];
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('contractors_package')
        .select(
          `
          *,
          usage_count:contractors(count)
        `,
        )
        .order('name');

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      // Transform the data to include usage_count as a number
      const packagesWithUsage: ContractorPackageWithUsageCount[] = (
        data || []
      ).map((pkg) => ({
        ...pkg,
        usage_count: Array.isArray(pkg.usage_count)
          ? pkg.usage_count.length
          : 0,
      }));

      return {
        success: true,
        data: packagesWithUsage,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get a specific contractor package by ID
   */
  static async getById(id: string): Promise<{
    success: boolean;
    data?: ContractorPackage;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('contractors_package')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create a new contractor package
   */
  static async create(packageData: ContractorPackageInsert): Promise<{
    success: boolean;
    data?: ContractorPackage;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('contractors_package')
        .insert(packageData)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update an existing contractor package
   */
  static async update(
    id: string,
    updates: ContractorPackageUpdate,
  ): Promise<{
    success: boolean;
    data?: ContractorPackage;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('contractors_package')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Delete a contractor package (only if no contractors are using it)
   */
  static async delete(id: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // First check if any contractors are using this package
      const { data: contractors, error: checkError } = await supabase
        .from('contractors')
        .select('id')
        .eq('package_id', id)
        .limit(1);

      if (checkError) {
        return {
          success: false,
          error: checkError.message,
        };
      }

      if (contractors && contractors.length > 0) {
        return {
          success: false,
          error:
            'Cannot delete package that is currently assigned to contractors',
        };
      }

      const { error } = await supabase
        .from('contractors_package')
        .delete()
        .eq('id', id);

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get the package assigned to a specific contractor
   */
  static async getContractorPackage(contractorId: string): Promise<{
    success: boolean;
    data?: ContractorPackage;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('contractors')
        .select(
          `
          package_id,
          contractors_package (*)
        `,
        )
        .eq('id', contractorId)
        .single();

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data: data?.contractors_package || undefined,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Assign a package to a contractor
   */
  static async assignToContractor(
    contractorId: string,
    packageId: string,
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Verify the package exists and is active
      const packageResult = await this.getById(packageId);
      if (!packageResult.success || !packageResult.data?.is_active) {
        return {
          success: false,
          error: 'Package not found or is inactive',
        };
      }

      const { error } = await supabase
        .from('contractors')
        .update({ package_id: packageId })
        .eq('id', contractorId);

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Remove package assignment from a contractor (sets to null)
   */
  static async removeFromContractor(contractorId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const { error } = await supabase
        .from('contractors')
        .update({ package_id: null })
        .eq('id', contractorId);

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get pricing for a contractor (using the database function)
   */
  static async getContractorPricing(contractorId: string): Promise<{
    success: boolean;
    data?: number;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase.rpc('get_contractor_pricing', {
        contractor_id_param: contractorId,
      });

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data: data || 150.0, // Fallback to default if no data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
