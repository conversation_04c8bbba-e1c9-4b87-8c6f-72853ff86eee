'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

// This page now redirects to the unified complaints page
// Admin complaints are now handled in the unified /complaints route
export default function AdminComplaintRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the unified complaints page
    router.replace('/complaints');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-lg font-semibold mb-2">Redirecting...</h2>
        <p className="text-muted-foreground">
          You are being redirected to the updated complaints page.
        </p>
      </div>
    </div>
  );
}
