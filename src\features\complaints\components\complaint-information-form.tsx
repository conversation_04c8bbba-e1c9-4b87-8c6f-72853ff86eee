'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUploadWithPreview } from '@/components/ui/file-upload-with-preview';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PhotoViewer } from '@/components/ui/photo-viewer';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SubscriptionStatusIndicator } from '@/components/ui/subscription-status-indicator';
import { useUserWithProfile } from '@/features/auth/hooks/use-auth';
import { usePMACertificates } from '@/features/pma-management/hooks/use-pma-certificates';
import { useProjectDetails } from '@/features/projects/hooks/use-project-details';
import { useContractorProfile } from '@/hooks/use-contractor-profile';
import { useMultiplePmaSubscriptionAccess } from '@/hooks/use-pma-subscriptions';
import { uploadToOBS } from '@/lib/obs-upload';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useCreateComplaint } from '../hooks/use-complaints-simple';
import { CreateComplaintInput, createComplaintInputSchema } from '../schemas';

interface ComplaintInformationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function ComplaintInformationForm({
  onSuccess,
  onCancel,
}: ComplaintInformationFormProps) {
  const [beforePhotos, setBeforePhotos] = useState<File[]>([]);
  const [uploadedBeforeUrls, setUploadedBeforeUrls] = useState<string[]>([]);
  const [isUploadingBefore, setIsUploadingBefore] = useState(false);
  const [selectedPMAId, setSelectedPMAId] = useState<string>('');

  // Helper to uniquely identify a File instance
  const fileKey = (f: File) => `${f.name}-${f.size}-${f.lastModified}`;

  // Hooks for data
  const { data: userWithProfile } = useUserWithProfile();
  const { selectedProjectId } = useProjectContext();
  const { data: projectDetails } = useProjectDetails(selectedProjectId || '');
  const { data: pmaData } = usePMACertificates(selectedProjectId, 1, 100); // Get all PMAs for the project

  // Get subscription status for all PMAs
  const pmaIds = pmaData?.data.map((pma) => pma.id) || [];
  const { data: subscriptionStatuses } =
    useMultiplePmaSubscriptionAccess(pmaIds);

  // Get contractor profile data if user is a contractor
  const isContractor = userWithProfile?.profile?.user_role === 'contractor';
  const { data: contractorProfile } = useContractorProfile(
    userWithProfile?.id,
    isContractor,
  );

  const createComplaintMutation = useCreateComplaint();
  const t = useTranslations('complaints.form');

  const form = useForm<CreateComplaintInput>({
    resolver: zodResolver(createComplaintInputSchema),
    defaultValues: {
      email: '',
      date: new Date(),
      expected_completion_date: new Date(),
      contractor_name: '',
      location: '',
      description: '',
      involves_mantrap: false,
      no_pma_lif: '',
      actual_completion_date: undefined,
      status: 'open',
      repair_cost: 0,
    },
  });

  // Auto-fill user and project data when available
  useEffect(() => {
    if (userWithProfile?.email) {
      form.setValue('email', userWithProfile.email);
    }

    // Use the user's name from the profile (users table)
    if (userWithProfile?.profile?.name) {
      form.setValue('contractor_name', userWithProfile.profile.name);
    }
  }, [userWithProfile, form]);

  // Handle PMA selection and auto-fill location
  const handlePMASelection = (pmaId: string) => {
    setSelectedPMAId(pmaId);
    const selectedPMA = pmaData?.data.find((pma) => pma.id === pmaId);
    if (selectedPMA) {
      form.setValue('pma_id', pmaId);
      form.setValue('no_pma_lif', selectedPMA.pma_number || '');
      if (selectedPMA.location) {
        form.setValue('location', selectedPMA.location);
      }
    }
  };

  // Check if current PMA selection has access
  const selectedPmaAccess =
    selectedPMAId && subscriptionStatuses
      ? subscriptionStatuses[selectedPMAId]
      : null;
  const canSubmit = !selectedPMAId || (selectedPmaAccess?.hasAccess ?? false);

  const handleBeforePhotoUpload = async (files: File[]) => {
    // Just update the state without uploading immediately
    // This allows users to preview files before form submission
    const existingKeys = new Set(beforePhotos.map(fileKey));
    const newlyAdded = files.filter((f) => !existingKeys.has(fileKey(f)));

    // Merge without duplicates to maintain a stable list for the dropzone
    const deduped = [
      ...beforePhotos,
      ...newlyAdded.filter(
        (f, idx, arr) =>
          arr.findIndex((x) => fileKey(x) === fileKey(f)) === idx,
      ),
    ];

    setBeforePhotos(deduped);

    // Note: Upload will happen during form submission, not immediately
    // This prevents automatic form submission when files are selected
  };

  const uploadBeforeFiles = async (files: File[]): Promise<string[]> => {
    const newUrls: string[] = [];

    try {
      for (const file of files) {
        const url = await uploadToOBS({
          file,
          folder: 'complaints/before-repair',
        });
        newUrls.push(url);
      }

      // Update the uploaded URLs state for potential re-renders
      setUploadedBeforeUrls(newUrls);
      return newUrls;
    } catch (error) {
      console.error('Upload error:', error);
      throw error; // Re-throw to handle in submission
    }
  };

  const removeBeforePhoto = (index: number) => {
    setBeforePhotos((prev) => prev.filter((_, i) => i !== index));
    setUploadedBeforeUrls((prev) => prev.filter((_, i) => i !== index));
  };
  const onSubmit = async (data: CreateComplaintInput) => {
    try {
      // Upload files first if any are selected
      let finalUploadedUrls: string[] = [];

      if (beforePhotos.length > 0) {
        setIsUploadingBefore(true);
        finalUploadedUrls = await uploadBeforeFiles(beforePhotos);
      }

      await createComplaintMutation.mutateAsync({
        ...data,
        beforeRepairFiles: beforePhotos,
        beforeRepairUrls: finalUploadedUrls,
      });

      onSuccess();
    } catch (error) {
      // Error handling is managed by the mutation hook
      console.error('Submission error:', error);
    } finally {
      setIsUploadingBefore(false);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6 max-w-5xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* A. Complaint Information */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4 bg-blue-50">
            <CardTitle className="text-lg font-semibold text-blue-800 flex items-center gap-2">
              {t('sectionA.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6">
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.email')}
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  className={cn(
                    'h-10 bg-gray-50',
                    form.formState.errors.email &&
                      'border-red-500 focus-visible:ring-red-500',
                  )}
                  placeholder={t('placeholders.email')}
                  readOnly
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contactNumber"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.name')}
                </Label>{' '}
                <Input
                  id="contactNumber"
                  {...form.register('contractor_name')}
                  className="h-10 bg-gray-50"
                  placeholder="Ahmad Zainuddin"
                  readOnly
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.complaintDate')}
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full h-10 justify-start text-left font-normal',
                        !form.watch('date') && 'text-muted-foreground',
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {form.watch('date') ? (
                        format(form.watch('date'), 'yyyy-MM-dd')
                      ) : (
                        <span>{t('placeholders.selectDate')}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={form.watch('date')}
                      onSelect={(date) =>
                        form.setValue('date', date || new Date())
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.agency')}
                </Label>{' '}
                <Input
                  value={
                    projectDetails?.agency?.name ||
                    (projectDetails ? 'No agency assigned' : 'Loading...')
                  }
                  className="h-10 bg-gray-50"
                  placeholder={t('placeholders.selectAgency')}
                  readOnly
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contractorCompanyName"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.contractorCompanyName')}
                </Label>{' '}
                <Input
                  id="contractorCompanyName"
                  value={contractorProfile?.contractor?.name || ''}
                  className="h-10 bg-gray-50"
                  placeholder="Syarikat Lif Teknologi Sdn Bhd"
                  readOnly
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="location"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionA.location')}
              </Label>
              <Input
                id="location"
                {...form.register('location')}
                className="h-10 bg-gray-50"
                placeholder=""
                readOnly
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="noPmaLif"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionA.pmaNumber')}
              </Label>{' '}
              <Select value={selectedPMAId} onValueChange={handlePMASelection}>
                <SelectTrigger className="h-10">
                  <SelectValue
                    placeholder={t('placeholders.selectPMA') || 'Select PMA'}
                  />
                </SelectTrigger>
                <SelectContent>
                  {pmaData?.data.map((pma) => {
                    const subscriptionStatus = subscriptionStatuses?.[pma.id];
                    const hasAccess = subscriptionStatus?.hasAccess ?? false;
                    return (
                      <SelectItem
                        key={pma.id}
                        value={pma.id}
                        disabled={!hasAccess}
                        className={cn(
                          'flex flex-col items-start gap-1 py-3',
                          !hasAccess && 'opacity-50',
                        )}
                      >
                        <div className="flex items-center justify-between w-full">
                          <span>{pma.pma_number || 'Unknown PMA Number'}</span>
                          {(subscriptionStatus?.status === 'pending_payment' ||
                            subscriptionStatus?.status === 'cancelled' ||
                            subscriptionStatus?.status === 'suspended') && (
                            <span className="text-xs text-orange-600 ml-2">
                              Payment pending
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {/* Show subscription status for selected PMA */}
              {selectedPMAId && selectedPmaAccess && (
                <div className="mt-2">
                  <SubscriptionStatusIndicator
                    status={selectedPmaAccess.status}
                    hasAccess={selectedPmaAccess.hasAccess}
                    gracePeriodEnds={selectedPmaAccess.gracePeriodEnds}
                    size="sm"
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionA.damageDescription')}
              </Label>
              <Input
                {...form.register('description')}
                className="h-10"
                placeholder=""
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionA.expectedCompletionDate')}
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full h-10 justify-start text-left font-normal',
                      !form.watch('expected_completion_date') &&
                        'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('expected_completion_date') ? (
                      format(
                        form.watch('expected_completion_date'),
                        'yyyy-MM-dd',
                      )
                    ) : (
                      <span>{t('placeholders.selectDate')}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={form.watch('expected_completion_date')}
                    onSelect={(date) =>
                      form.setValue(
                        'expected_completion_date',
                        date || new Date(),
                      )
                    }
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.involvesManTrap')}
                </Label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="true"
                      checked={form.watch('involves_mantrap') === true}
                      onChange={() => form.setValue('involves_mantrap', true)}
                      className="w-4 h-4"
                    />
                    <span className="text-sm">{t('sectionA.yes')}</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="false"
                      checked={form.watch('involves_mantrap') === false}
                      onChange={() => form.setValue('involves_mantrap', false)}
                      className="w-4 h-4"
                    />
                    <span className="text-sm">{t('sectionA.no')}</span>
                  </label>
                </div>
              </div>

              {/* Before Photo Upload */}
              <div className="mt-4 space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.beforePhoto')}
                </Label>
                <FileUploadWithPreview
                  onFilesChange={handleBeforePhotoUpload}
                  files={beforePhotos}
                  accept=".jpg,.jpeg,.png,.pdf"
                  maxSize={10 * 1024 * 1024} // 10MB
                  maxFiles={5}
                  onFileRemove={removeBeforePhoto}
                  disabled={isUploadingBefore}
                  allowPreview={true}
                  className="w-full"
                />

                {/* Display upload status */}
                {isUploadingBefore && (
                  <div className="text-sm text-blue-600 flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
                    Uploading photos during submission...
                  </div>
                )}

                {/* Show preview message */}
                {beforePhotos.length > 0 &&
                  !isUploadingBefore &&
                  uploadedBeforeUrls.length === 0 && (
                    <div className="text-sm text-green-600 flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                      <span>📸</span>
                      <span>
                        {beforePhotos.length} file(s) selected. Files will be
                        uploaded when you submit the form.
                      </span>
                    </div>
                  )}

                {/* Display uploaded files with PhotoViewer - Only show after successful upload */}
                {uploadedBeforeUrls.length > 0 && !isUploadingBefore && (
                  <div className="mt-4">
                    <PhotoViewer
                      photos={uploadedBeforeUrls.map((url, index) => ({
                        url,
                        name:
                          url.split('/').pop() ||
                          `Before Repair Photo ${index + 1}`,
                      }))}
                      title="Successfully Uploaded Photos"
                    />
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Important Note */}
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-orange-600 mt-1">⚠️</div>
              <div className="text-sm">
                <div className="font-medium text-orange-800 mb-1">
                  {t('notes.title')}
                </div>
                <ul className="space-y-1 text-orange-700">
                  <li>• {t('notes.required')}</li>
                  <li>• {t('notes.proofRequired')}</li>
                  <li>• {t('notes.autoSubmit')}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="w-full sm:w-auto px-6"
          >
            {t('buttons.back')}
          </Button>
          <Button
            type="submit"
            disabled={
              createComplaintMutation.isPending ||
              isUploadingBefore ||
              !canSubmit
            }
            className="w-full sm:w-auto px-8 bg-blue-600 hover:bg-blue-700"
          >
            {createComplaintMutation.isPending || isUploadingBefore ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {isUploadingBefore
                  ? 'Uploading files...'
                  : t('buttons.submitting')}
              </>
            ) : (
              <>{t('buttons.submit')}</>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
