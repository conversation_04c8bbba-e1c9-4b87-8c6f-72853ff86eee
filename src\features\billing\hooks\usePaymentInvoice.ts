import { useQuery } from '@tanstack/react-query';
import { getPaymentInvoiceData } from '../services/payment-records-api.service';
import type { PaymentRecordWithSubscription } from '../types/payment-records';

interface UsePaymentInvoiceParams {
  paymentId?: string;
  enabled?: boolean;
}

/**
 * Hook for fetching payment invoice data
 * Works for all payment records regardless of status, uses created_at date
 */
export function usePaymentInvoice({
  paymentId,
  enabled = true,
}: UsePaymentInvoiceParams) {
  return useQuery({
    queryKey: ['payment-invoice', paymentId],
    queryFn: (): Promise<PaymentRecordWithSubscription> => {
      return getPaymentInvoiceData(paymentId!);
    },
    enabled: enabled && !!paymentId,
    staleTime: 30 * 60 * 1000, // 30 minutes - invoices don't change frequently
    gcTime: 60 * 60 * 1000, // 1 hour
  });
}
