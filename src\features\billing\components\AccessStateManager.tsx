'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { PmaSubscriptionWithAccess } from '@/types/billing';
import { getDaysRemainingInGracePeriod } from '@/types/billing';
import type { LucideIcon } from 'lucide-react';
import {
  AlertTriangle,
  Ban,
  CheckCircle,
  Clock,
  CreditCard,
  RefreshCw,
} from 'lucide-react';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

interface ActionConfig {
  icon: LucideIcon;
  label: string;
  variant:
    | 'destructive'
    | 'default'
    | 'outline'
    | 'link'
    | 'secondary'
    | 'ghost';
  onClick: () => void;
  className?: string;
  disabled?: boolean;
}

export interface AccessStateManagerProps {
  subscription: PmaSubscriptionWithAccess;
  projectName?: string;
  onPayNow?: () => void;
  onReactivate?: () => void;
  onCancel?: () => void;
  className?: string;
  variant?: 'card' | 'compact' | 'alert';
}

/**
 * Centralized component for handling different access states with contextual actions
 */
export function AccessStateManager({
  subscription,
  projectName,
  onPayNow,
  onReactivate,
  onCancel,
  className,
  variant = 'card',
}: AccessStateManagerProps) {
  const daysRemaining = getDaysRemainingInGracePeriod(
    subscription.grace_period_ends,
  );
  const monthlyAmount = subscription.amount || 0;

  // Determine the primary action and messaging based on state
  const stateConfig = getStateConfig(
    subscription,
    daysRemaining,
    monthlyAmount,
  );

  if (variant === 'compact') {
    return (
      <div
        className={cn(
          'flex items-center justify-between p-3 border rounded-lg',
          className,
        )}
      >
        <div className="flex items-center gap-3">
          <ProjectAccessIndicator
            status={subscription.status}
            gracePeriodEnds={subscription.grace_period_ends}
            showLabel={false}
          />
          <div>
            <p className="text-sm font-medium">{projectName || 'Project'}</p>
            <p className="text-xs text-muted-foreground">
              {stateConfig.message}
            </p>
          </div>
        </div>
        {stateConfig.action && (
          <Button
            size="sm"
            variant={stateConfig.action.variant}
            onClick={stateConfig.action.onClick}
            className={stateConfig.action.className}
          >
            <stateConfig.action.icon className="h-4 w-4 mr-1" />
            {stateConfig.action.label}
          </Button>
        )}
      </div>
    );
  }

  if (variant === 'alert') {
    return (
      <Alert className={cn(stateConfig.alertClassName, className)}>
        <stateConfig.icon className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{stateConfig.title}</p>
              <p className="text-sm mt-1">{stateConfig.message}</p>
            </div>
            {stateConfig.action && (
              <Button
                size="sm"
                variant={stateConfig.action.variant}
                onClick={stateConfig.action.onClick}
                className={stateConfig.action.className}
              >
                <stateConfig.action.icon className="h-4 w-4 mr-1" />
                {stateConfig.action.label}
              </Button>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Card variant (default)
  return (
    <Card className={cn(stateConfig.cardClassName, className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <stateConfig.icon
              className={cn('h-5 w-5', stateConfig.iconColor)}
            />
            <CardTitle className="text-lg">{stateConfig.title}</CardTitle>
          </div>
          <ProjectAccessIndicator
            status={subscription.status}
            gracePeriodEnds={subscription.grace_period_ends}
            size="sm"
          />
        </div>
        <CardDescription>{stateConfig.message}</CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* Subscription Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            {/* Monthly amount hidden as per requirements */}
            {/* <div>
              <p className="text-muted-foreground">Monthly Amount</p>
              <p className="font-medium">RM {monthlyAmount.toFixed(2)}</p>
            </div> */}
            <div>
              <p className="text-muted-foreground">Status</p>
              <Badge variant="outline" className="text-xs">
                {subscription.status.replace('_', ' ')}
              </Badge>
            </div>
          </div>

          {/* Grace Period Warning */}
          {subscription.isInGracePeriod && daysRemaining !== null && (
            <>
              <Separator />
              <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <Clock className="h-4 w-4 text-yellow-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    Grace Period Active
                  </p>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300">
                    {daysRemaining > 0
                      ? `${daysRemaining} day${daysRemaining !== 1 ? 's' : ''} remaining until access is suspended`
                      : 'Grace period has ended'}
                  </p>
                </div>
              </div>
            </>
          )}

          {/* Next Billing Date */}
          {subscription.next_billing_date &&
            subscription.status === 'active' && (
              <>
                <Separator />
                <div className="text-sm">
                  <p className="text-muted-foreground">Next Billing Date</p>
                  <p className="font-medium">
                    {new Date(
                      subscription.next_billing_date,
                    ).toLocaleDateString()}
                  </p>
                </div>
              </>
            )}

          {/* Action Buttons */}
          {stateConfig.actions && stateConfig.actions.length > 0 && (
            <>
              <Separator />
              <div className="flex flex-col gap-2">
                {stateConfig.actions.map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant}
                    onClick={action.onClick}
                    className={cn('justify-start', action.className)}
                    disabled={action.disabled}
                  >
                    <action.icon className="h-4 w-4 mr-2" />
                    {action.label}
                  </Button>
                ))}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );

  function getStateConfig(
    subscription: PmaSubscriptionWithAccess,
    daysRemaining: number | null,
    _monthlyAmount: number,
  ) {
    const actions: ActionConfig[] = [];

    // Pay Now action (for grace period, pending, failed payments, and trial)
    if (
      onPayNow &&
      (subscription.isInGracePeriod ||
        subscription.status === 'pending_payment' ||
        subscription.status === 'trial')
    ) {
      actions.push({
        icon: CreditCard,
        label: subscription.isInGracePeriod ? 'Pay Now (Urgent)' : 'Pay Now',
        variant: subscription.isInGracePeriod ? 'destructive' : 'default',
        onClick: onPayNow,
        className: subscription.isInGracePeriod
          ? 'bg-red-600 hover:bg-red-700'
          : '',
      });
    }

    // Reactivate action (for cancelled/suspended)
    if (
      onReactivate &&
      (subscription.status === 'cancelled' ||
        subscription.status === 'suspended')
    ) {
      actions.push({
        icon: RefreshCw,
        label: 'Reactivate Subscription',
        variant: 'default',
        onClick: onReactivate,
        className: '',
      });
    }

    // Cancel action (for active subscriptions)
    if (onCancel && subscription.status === 'active') {
      actions.push({
        icon: Ban,
        label: 'Cancel Subscription',
        variant: 'outline',
        onClick: onCancel,
        className:
          'text-red-600 hover:text-red-700 border-red-200 hover:border-red-300',
      });
    }

    // Primary action for compact/alert variants
    const primaryAction = actions[0];

    switch (subscription.status) {
      case 'active':
        return {
          icon: CheckCircle,
          iconColor: 'text-green-500',
          title: 'Project Access Active',
          message: subscription.accessAllowed
            ? 'You have full access to all project features.'
            : 'Access verification in progress.',
          cardClassName: 'border-green-200 dark:border-green-800',
          alertClassName:
            'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950',
          action: primaryAction,
          actions,
        };

      case 'grace_period':
        return {
          icon: Clock,
          iconColor: 'text-yellow-500',
          title: 'Payment Required - Grace Period Active',
          message:
            daysRemaining !== null && daysRemaining > 0
              ? `Your subscription payment failed. You have ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''} of continued access. Pay now to avoid service interruption.`
              : 'Your grace period has ended. Pay now to restore access.',
          cardClassName: 'border-yellow-200 dark:border-yellow-800',
          alertClassName:
            'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950',
          action: primaryAction,
          actions,
        };

      case 'pending_payment':
        return {
          icon: CreditCard,
          iconColor: 'text-blue-500',
          title: 'Payment Pending',
          message:
            'Your subscription is awaiting payment. Complete payment to maintain access.',
          cardClassName: 'border-blue-200 dark:border-blue-800',
          alertClassName:
            'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950',
          action: primaryAction,
          actions,
        };

      case 'trial':
        return {
          icon: CreditCard,
          iconColor: 'text-blue-500',
          title: 'Trial Period Active',
          message:
            'Your trial period is active. You can pay now to convert to a full subscription.',
          cardClassName: 'border-blue-200 dark:border-blue-800',
          alertClassName:
            'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950',
          action: primaryAction,
          actions,
        };

      case 'suspended':
        return {
          icon: AlertTriangle,
          iconColor: 'text-red-500',
          title: 'Access Suspended',
          message:
            'Your project access has been suspended due to payment issues. Reactivate your subscription to restore access.',
          cardClassName: 'border-red-200 dark:border-red-800',
          alertClassName:
            'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950',
          action: primaryAction,
          actions,
        };

      case 'cancelled':
        return {
          icon: Ban,
          iconColor: 'text-gray-500',
          title: 'Subscription Cancelled',
          message:
            'Your subscription has been cancelled. Reactivate to regain access to project features.',
          cardClassName: 'border-gray-200 dark:border-gray-800',
          alertClassName:
            'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950',
          action: primaryAction,
          actions,
        };

      default:
        return {
          icon: AlertTriangle,
          iconColor: 'text-gray-500',
          title: 'Status Unknown',
          message:
            'Unable to determine subscription status. Please contact support.',
          cardClassName: 'border-gray-200 dark:border-gray-800',
          alertClassName:
            'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950',
          action: primaryAction,
          actions,
        };
    }
  }
}
